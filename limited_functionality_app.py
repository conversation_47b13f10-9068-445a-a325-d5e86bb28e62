"""
Limited Functionality Version - Works with Client Portal Gateway Only
This version shows what's possible with current setup and guides user to full setup
"""

import logging
import requests
import urllib3
from auth import IBAuth
from account import AccountManager

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LimitedOptionsTrader:
    def __init__(self):
        self.auth = IBAuth()
        self.session = None
        self.account_manager = None
        
    def initialize(self):
        """Initialize the trading application"""
        print("Initializing Interactive Brokers Options Trading Application...")
        print("🔍 Checking available functionality...")
        
        # Check authentication
        if not self.auth.is_authenticated():
            print("\n❌ Not authenticated with Interactive Brokers")
            print("Please login via browser at: https://localhost:5000")
            print("Then restart this application.")
            return False
        
        print("✅ Authentication successful")
        
        # Get authenticated session
        self.session = self.auth.get_session()
        if not self.session:
            print("❌ Failed to get authenticated session")
            return False
        
        # Initialize managers
        self.account_manager = AccountManager(self.session)
        
        print("✅ Application initialized successfully")
        return True
    
    def display_menu(self):
        """Display the main menu options"""
        print("\n===== IB Options Trading Menu (Limited Mode) =====")
        print("✅ 1. Fetch Account Balances")
        print("❌ 2. Fetch Open Option Positions (Requires IB Gateway)")
        print("❌ 3. Fetch Option Chain for Symbol (Requires IB Gateway)")
        print("❌ 4. Get Option Market Data (Requires IB Gateway)")
        print("❌ 5. Place Option Order (Requires IB Gateway)")
        print("❌ 6. Check Order Status (Requires IB Gateway)")
        print("❌ 7. Cancel Order (Requires IB Gateway)")
        print("✅ 8. Setup Guide for Full Functionality")
        print("✅ 9. Exit")
        print("=" * 55)
        return input("Enter your choice (1, 8, or 9): ")
    
    def fetch_account_balances(self):
        """Fetch and display account balances"""
        print("\nFetching account balances...")
        try:
            balances = self.account_manager.get_account_balances()
            if balances:
                self.account_manager.display_account_balances(balances)
                return True
            else:
                print("❌ Could not retrieve account balances")
                return False
        except Exception as e:
            print(f"❌ Error fetching balances: {e}")
            return False
    
    def show_setup_guide(self):
        """Show setup guide for full functionality"""
        print("\n" + "=" * 60)
        print("🚀 SETUP GUIDE FOR FULL OPTIONS TRADING FUNCTIONALITY")
        print("=" * 60)
        
        print("\n📋 CURRENT STATUS:")
        print("✅ Client Portal Gateway: Running")
        print("✅ Authentication: Working")
        print("✅ Account Access: Working")
        print("❌ Options Trading: Limited (needs IB Gateway)")
        
        print("\n🎯 TO ENABLE FULL FUNCTIONALITY:")
        print("\n1. DOWNLOAD IB GATEWAY:")
        print("   → Go to: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php")
        print("   → Download 'IB Gateway for Windows'")
        print("   → Install with default settings")
        
        print("\n2. CONFIGURE IB GATEWAY:")
        print("   → Start IB Gateway (desktop icon)")
        print("   → Login with your LIVE account credentials")
        print("   → Go to Configure → API → Settings")
        print("   → ✅ Enable ActiveX and Socket Clients")
        print("   → ✅ Socket Port: 7497 (paper trading)")
        print("   → ✅ Trusted IPs: Add 127.0.0.1")
        print("   → ✅ Read-Only API: Checked")
        print("   → Click OK and Apply")
        
        print("\n3. RUN BOTH GATEWAYS:")
        print("   → Keep Client Portal Gateway running (current)")
        print("   → Start IB Gateway (new)")
        print("   → Both should run simultaneously")
        
        print("\n4. UPGRADE YOUR APPLICATION:")
        print("   → Install: pip install ibapi")
        print("   → Use the full version of the application")
        
        print("\n🎊 AFTER SETUP, YOU'LL HAVE:")
        print("✅ Real-time option chains")
        print("✅ Live market data with Greeks")
        print("✅ Option order placement")
        print("✅ Position tracking")
        print("✅ Order management")
        
        print("\n💡 WHY TWO GATEWAYS?")
        print("• Client Portal Gateway: Web API, authentication, account info")
        print("• IB Gateway: Full trading API, market data, options")
        print("• Together: Complete trading platform")
        
        print("\n" + "=" * 60)
    
    def run(self):
        """Run the main application loop"""
        if not self.initialize():
            return
        
        print("\n🚀 Welcome to Interactive Brokers Options Trading Application!")
        print("⚠️  RUNNING IN LIMITED MODE")
        print("💡 For full options trading, see Setup Guide (option 8)")
        
        while True:
            try:
                choice = self.display_menu()
                
                if choice == '1':
                    success = self.fetch_account_balances()
                    if success:
                        print("\n✅ Account balances retrieved successfully!")
                        print("💡 This shows your Client Portal Gateway is working correctly.")
                    
                elif choice in ['2', '3', '4', '5', '6', '7']:
                    print(f"\n❌ Option {choice} requires IB Gateway")
                    print("💡 See Setup Guide (option 8) to enable this functionality")
                    
                elif choice == '8':
                    self.show_setup_guide()
                    
                elif choice == '9':
                    print("\nThank you for using IB Options Trading Application!")
                    print("💡 Install IB Gateway for full functionality!")
                    break
                    
                else:
                    print("Invalid choice. Please enter 1, 8, or 9.")
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\nApplication interrupted by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                print(f"An error occurred: {e}")

if __name__ == "__main__":
    trader = LimitedOptionsTrader()
    trader.run()
