"""
Order management module for Interactive Brokers Options Trading
"""

import json
import logging
from config import BASE_URL, ORDER_TYPES, ORDER_SIDES, DEFAULT_TIF, USE_ADAPTIVE, DEFAULT_OUTSIDE_RTH

logger = logging.getLogger(__name__)

class OrderManager:
    def __init__(self, session):
        self.session = session
    
    def validate_order(self, order_data):
        """Validate order before placing"""
        try:
            resp = self.session.post(
                f"{BASE_URL}/iserver/account/orders/whatif",
                data=json.dumps(order_data),
                headers={"Content-Type": "application/json"}
            )
            
            if resp.ok:
                validation_result = resp.json()
                logger.info(f"Order validation successful: {validation_result}")
                return validation_result
            else:
                logger.error(f"Order validation failed: {resp.status_code} - {resp.text}")
                return None
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return None
    
    def place_option_order(self, conid, order_type, side, quantity, price=None, stop_price=None):
        """Place a single-leg option order"""
        try:
            order_data = {
                "conid": str(conid),
                "orderType": ORDER_TYPES.get(order_type.upper(), order_type),
                "side": ORDER_SIDES.get(side.upper(), side),
                "quantity": quantity,
                "tif": DEFAULT_TIF,
                "useAdaptive": USE_ADAPTIVE,
                "outsideRTH": DEFAULT_OUTSIDE_RTH
            }
            
            # Add price for limit orders
            if order_type.upper() in ["LIMIT", "LMT"] and price is not None:
                order_data["price"] = price
            
            # Add stop price for stop orders
            if order_type.upper() in ["STOP", "STP"] and stop_price is not None:
                order_data["auxPrice"] = stop_price
            
            # Add both prices for stop limit orders
            if order_type.upper() in ["STOP_LIMIT", "STP LMT"]:
                if price is not None:
                    order_data["price"] = price
                if stop_price is not None:
                    order_data["auxPrice"] = stop_price
            
            # Validate order first
            validation = self.validate_order(order_data)
            if not validation:
                logger.error("Order validation failed")
                return None
            
            # Place the order
            resp = self.session.post(
                f"{BASE_URL}/iserver/account/orders",
                data=json.dumps([order_data]),
                headers={"Content-Type": "application/json"}
            )
            
            if resp.ok:
                order_result = resp.json()
                logger.info(f"Order placed successfully: {order_result}")
                return order_result
            else:
                logger.error(f"Failed to place order: {resp.status_code} - {resp.text}")
                return None
        except Exception as e:
            logger.error(f"Error placing option order: {e}")
            return None
    
    def place_market_order(self, conid, side, quantity):
        """Place a market order for options"""
        return self.place_option_order(conid, "MARKET", side, quantity)
    
    def place_limit_order(self, conid, side, quantity, limit_price):
        """Place a limit order for options"""
        return self.place_option_order(conid, "LIMIT", side, quantity, price=limit_price)
    
    def place_stop_order(self, conid, side, quantity, stop_price):
        """Place a stop order for options"""
        return self.place_option_order(conid, "STOP", side, quantity, stop_price=stop_price)
    
    def place_stop_limit_order(self, conid, side, quantity, limit_price, stop_price):
        """Place a stop limit order for options"""
        return self.place_option_order(conid, "STOP_LIMIT", side, quantity, price=limit_price, stop_price=stop_price)
    
    def get_orders(self):
        """Get all orders"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/account/orders")
            if resp.ok:
                orders = resp.json()
                logger.info(f"Retrieved {len(orders)} orders")
                return orders
            else:
                logger.error(f"Failed to get orders: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return None
    
    def get_options_orders(self):
        """Get only options orders"""
        try:
            orders = self.get_orders()
            if not orders:
                return []
            
            options_orders = []
            for order in orders:
                if order.get('assetClass') == 'OPT':
                    options_orders.append({
                        'order_id': order.get('orderId'),
                        'conid': order.get('conid'),
                        'symbol': order.get('ticker'),
                        'side': order.get('side'),
                        'order_type': order.get('orderType'),
                        'quantity': order.get('totalSize'),
                        'filled_quantity': order.get('filledQuantity', 0),
                        'remaining_quantity': order.get('remainingQuantity'),
                        'status': order.get('status'),
                        'avg_price': order.get('avgPrice'),
                        'limit_price': order.get('price'),
                        'expiry': order.get('expiry'),
                        'strike': order.get('strike'),
                        'right': order.get('right'),
                        'time_in_force': order.get('timeInForce'),
                        'order_time': order.get('lastExecutionTime')
                    })
            
            logger.info(f"Found {len(options_orders)} options orders")
            return options_orders
        except Exception as e:
            logger.error(f"Error getting options orders: {e}")
            return []
    
    def display_orders(self, orders):
        """Display orders in a formatted way"""
        if not orders:
            print("No orders found")
            return
        
        print("\n===== Options Orders =====")
        print(f"{'Order ID':<12} {'Symbol':<8} {'Type':<4} {'Side':<4} {'Qty':<6} {'Filled':<6} {'Status':<10} {'Price':<10}")
        print("-" * 80)
        
        for order in orders:
            order_id = str(order['order_id'])[:10] if order['order_id'] else 'N/A'
            symbol = order['symbol'] or 'N/A'
            option_type = order['right'] or 'N/A'
            side = order['side'] or 'N/A'
            quantity = order['quantity'] or 0
            filled = order['filled_quantity'] or 0
            status = order['status'] or 'N/A'
            price = f"${order['avg_price']:.2f}" if order['avg_price'] else 'N/A'
            
            print(f"{order_id:<12} {symbol:<8} {option_type:<4} {side:<4} {quantity:<6} {filled:<6} {status:<10} {price:<10}")
        
        print("=" * 80)
    
    def cancel_order(self, order_id):
        """Cancel an order"""
        try:
            resp = self.session.delete(f"{BASE_URL}/iserver/account/orders/{order_id}")
            if resp.ok:
                result = resp.json()
                logger.info(f"Order {order_id} cancelled successfully")
                return result
            else:
                logger.error(f"Failed to cancel order {order_id}: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return None
    
    def modify_order(self, order_id, modifications):
        """Modify an existing order"""
        try:
            resp = self.session.post(
                f"{BASE_URL}/iserver/account/orders/{order_id}",
                data=json.dumps(modifications),
                headers={"Content-Type": "application/json"}
            )
            
            if resp.ok:
                result = resp.json()
                logger.info(f"Order {order_id} modified successfully")
                return result
            else:
                logger.error(f"Failed to modify order {order_id}: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error modifying order: {e}")
            return None
