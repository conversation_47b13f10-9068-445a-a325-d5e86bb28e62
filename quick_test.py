"""
Quick test to verify IB Gateway connection
"""

try:
    import requests
    import urllib3
    
    # Disable SSL warnings
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())
    
    print("🔍 Testing IB Gateway Connection...")
    
    session = requests.Session()
    session.verify = False
    
    # Test connection
    resp = session.get("https://localhost:5000/v1/api/iserver/auth/status", timeout=10)
    
    if resp.ok:
        auth_data = resp.json()
        if auth_data.get('authenticated'):
            print("✅ SUCCESS: Gateway connected and authenticated!")
            print("🚀 You can now run: python main.py")
        else:
            print("⚠️  Gateway connected but not authenticated")
            print("👉 Please login at: https://localhost:5000")
    else:
        print(f"❌ Gateway not responding (HTTP {resp.status_code})")
        print("👉 Make sure gateway is running: .\\bin\\run.bat root\\conf.yaml")

except requests.exceptions.ConnectionError:
    print("❌ Cannot connect to gateway")
    print("👉 Start gateway with: .\\bin\\run.bat root\\conf.yaml")
    print("👉 Then login at: https://localhost:5000")

except Exception as e:
    print(f"❌ Error: {e}")

input("\nPress Enter to close...")
