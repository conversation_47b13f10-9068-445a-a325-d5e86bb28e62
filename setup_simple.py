"""
Simple Windows Setup Script for Interactive Brokers Options Trading
"""

import os
import sys
import subprocess
import urllib.request
import zipfile

def download_client_portal_gateway():
    """Download and extract Client Portal Gateway"""
    gateway_url = "https://download2.interactivebrokers.com/portal/clientportal.gw.zip"
    gateway_file = "clientportal.gw.zip"
    
    if os.path.exists("clientportal.gw"):
        print("[OK] Client Portal Gateway already exists")
        return True
    
    try:
        print("Downloading Client Portal Gateway...")
        urllib.request.urlretrieve(gateway_url, gateway_file)
        print("[OK] Downloaded Client Portal Gateway")
        
        print("Extracting Client Portal Gateway...")
        with zipfile.ZipFile(gateway_file, 'r') as zip_ref:
            zip_ref.extractall(".")
        os.remove(gateway_file)
        print("[OK] Client Portal Gateway extracted")
        return True
    except Exception as e:
        print(f"[ERROR] Failed to download/extract gateway: {e}")
        return False

def create_startup_script():
    """Create Windows batch script to start gateway"""
    batch_content = """@echo off
echo Starting Interactive Brokers Client Portal Gateway...
echo.
echo IMPORTANT: After starting, go to https://localhost:5000 in your browser
echo and login with your PAPER TRADING credentials
echo.
cd /d "%~dp0clientportal.gw"
java -cp "dist/*" ibgateway.GWClient
pause
"""
    
    try:
        with open("start_gateway.bat", "w") as f:
            f.write(batch_content)
        print("[OK] Created start_gateway.bat")
        return True
    except Exception as e:
        print(f"[ERROR] Failed to create startup script: {e}")
        return False

def install_dependencies():
    """Install Python dependencies"""
    try:
        print("Installing Python dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("[OK] Dependencies installed")
        return True
    except subprocess.CalledProcessError:
        print("[ERROR] Failed to install dependencies")
        return False

def check_java():
    """Check if Java is installed"""
    try:
        result = subprocess.run(["java", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("[OK] Java is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("[WARNING] Java not found. Please install Java 8 or higher")
    print("Download from: https://www.oracle.com/java/technologies/downloads/")
    return False

def main():
    """Main setup function"""
    print("=" * 60)
    print(" Interactive Brokers Options Trading Setup")
    print("=" * 60)
    
    # Check Java
    print("\n[1/4] Checking Java...")
    java_ok = check_java()
    
    # Install dependencies
    print("\n[2/4] Installing dependencies...")
    if not install_dependencies():
        return False
    
    # Download gateway
    print("\n[3/4] Setting up Client Portal Gateway...")
    if not download_client_portal_gateway():
        return False
    
    # Create startup script
    print("\n[4/4] Creating startup scripts...")
    if not create_startup_script():
        return False
    
    print("\n" + "=" * 60)
    print(" SETUP COMPLETE - NEXT STEPS")
    print("=" * 60)
    
    print("\n1. Download and Install IB Gateway:")
    print("   Go to: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php")
    print("   Download Windows version and install")
    
    print("\n2. Setup Paper Trading Account:")
    print("   Login to: https://portal.interactivebrokers.com")
    print("   Go to Settings > Account Configuration > Paper Trading Account")
    print("   Note your Paper Trading Username and reset password")
    
    print("\n3. Start IB Gateway:")
    print("   Double-click IB Gateway icon")
    print("   Login with your LIVE account credentials")
    print("   Configure API: Enable API, Port 7497, Trusted IP 127.0.0.1")
    
    print("\n4. Start Client Portal Gateway:")
    print("   Run: start_gateway.bat")
    print("   Go to: https://localhost:5000")
    print("   Login with PAPER TRADING credentials")
    
    print("\n5. Test Connection:")
    print("   Run: python test_connection.py")
    
    print("\n6. Start Trading Application:")
    print("   Run: python main.py")
    
    if not java_ok:
        print("\n[WARNING] Please install Java before proceeding!")
    
    print("\n[SUCCESS] Setup completed!")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nSetup interrupted by user")
    except Exception as e:
        print(f"\n[ERROR] Setup failed: {e}")
        sys.exit(1)
