"""
Test what endpoints are available with Client Portal Gateway only
"""

import requests
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

def test_endpoints():
    """Test various IB API endpoints to see what's available"""
    print("🔍 Testing Available IB API Endpoints")
    print("=" * 50)
    
    session = requests.Session()
    session.verify = False
    base_url = "https://localhost:5000/v1/api"
    
    # List of endpoints to test
    endpoints = [
        # Authentication & Account
        ("/iserver/auth/status", "Authentication Status"),
        ("/portfolio/accounts", "Portfolio Accounts"),
        ("/iserver/accounts", "IServer Accounts"),
        ("/iserver/account/DUM602021/summary", "Account Summary"),
        
        # Market Data
        ("/iserver/marketdata/snapshot?conids=756733&fields=31", "Market Data Snapshot"),
        
        # Options & Securities
        ("/trsrv/secdef/search?symbol=SPY&secType=STK", "Security Search - Stock"),
        ("/trsrv/secdef/search?symbol=SPY&secType=OPT", "Security Search - Options"),
        ("/iserver/secdef/info?conid=756733&sectype=OPT", "Option Chain"),
        ("/iserver/secdef/strikes?conid=756733&sectype=OPT", "Option Strikes"),
        
        # Orders
        ("/iserver/account/orders", "Order Status"),
        
        # Positions
        ("/portfolio/positions", "Portfolio Positions"),
        ("/iserver/account/positions/0", "IServer Positions"),
        
        # Other
        ("/portal/iserver/account/pnl/partitioned", "P&L"),
        ("/hmds/history?conid=756733&period=1d&bar=1min", "Historical Data"),
    ]
    
    working_endpoints = []
    failed_endpoints = []
    
    for endpoint, description in endpoints:
        try:
            print(f"\nTesting: {description}")
            print(f"Endpoint: {endpoint}")
            
            resp = session.get(f"{base_url}{endpoint}", timeout=10)
            
            if resp.ok:
                print(f"✅ SUCCESS ({resp.status_code})")
                working_endpoints.append((endpoint, description))
                
                # Show sample response for successful endpoints
                try:
                    data = resp.json()
                    if isinstance(data, list):
                        print(f"   Response: List with {len(data)} items")
                        if data and len(data) > 0:
                            print(f"   Sample: {str(data[0])[:100]}...")
                    elif isinstance(data, dict):
                        print(f"   Response: Dict with keys: {list(data.keys())[:5]}")
                    else:
                        print(f"   Response: {str(data)[:100]}...")
                except:
                    print(f"   Response: {resp.text[:100]}...")
            else:
                print(f"❌ FAILED ({resp.status_code})")
                failed_endpoints.append((endpoint, description, resp.status_code))
                
        except requests.exceptions.Timeout:
            print("⏱️  TIMEOUT")
            failed_endpoints.append((endpoint, description, "TIMEOUT"))
        except Exception as e:
            print(f"❌ ERROR: {e}")
            failed_endpoints.append((endpoint, description, str(e)))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 ENDPOINT AVAILABILITY SUMMARY")
    print("=" * 50)
    
    print(f"\n✅ WORKING ENDPOINTS ({len(working_endpoints)}):")
    for endpoint, description in working_endpoints:
        print(f"   • {description}")
    
    print(f"\n❌ FAILED ENDPOINTS ({len(failed_endpoints)}):")
    for endpoint, description, error in failed_endpoints:
        print(f"   • {description} ({error})")
    
    # Recommendations
    print("\n" + "=" * 50)
    print("💡 RECOMMENDATIONS")
    print("=" * 50)
    
    if len(working_endpoints) >= 4:
        print("✅ Good news! Basic functionality is available.")
        print("✅ You can use account balances, positions, and order status.")
    
    if any("secdef/search" in ep for ep, _, _ in failed_endpoints):
        print("⚠️  Option search endpoints not available.")
        print("💡 For full options trading, install IB Gateway:")
        print("   1. Download: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php")
        print("   2. Install and configure for paper trading")
        print("   3. Run both Client Portal Gateway AND IB Gateway")
    
    if len(working_endpoints) < 3:
        print("❌ Limited functionality available.")
        print("💡 Check authentication at https://localhost:5000")
    
    return working_endpoints, failed_endpoints

if __name__ == "__main__":
    try:
        working, failed = test_endpoints()
        print(f"\n🎯 Result: {len(working)} working, {len(failed)} failed endpoints")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("💡 Make sure Client Portal Gateway is running and you're authenticated")
