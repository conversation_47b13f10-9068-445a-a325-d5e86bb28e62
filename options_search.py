"""
Options search and contract lookup module for Interactive Brokers
"""

import logging
from config import BASE_URL

logger = logging.getLogger(__name__)

class OptionsSearch:
    def __init__(self, session):
        self.session = session
    
    def get_underlying_conid(self, symbol):
        """Get ConID for underlying stock to fetch option chain"""
        try:
            resp = self.session.get(f"{BASE_URL}/trsrv/secdef/search", params={
                "symbol": symbol,
                "secType": "STK"
            })
            
            if resp.ok:
                results = resp.json()
                if results:
                    conid = results[0]['conid']
                    logger.info(f"Found underlying ConID {conid} for symbol {symbol}")
                    return conid
                else:
                    logger.warning(f"No results found for underlying symbol {symbol}")
                    return None
            else:
                logger.error(f"Failed to search for underlying: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting underlying ConID: {e}")
            return None
    
    def search_specific_option(self, underlying_symbol, expiry, strike, right):
        """Search for a specific option contract"""
        try:
            params = {
                "symbol": underlying_symbol,
                "secType": "OPT",
                "month": expiry,  # Format: YYYYMMDD
                "strike": str(strike),
                "right": right.upper()  # 'C' for Call, 'P' for Put
            }
            
            resp = self.session.get(f"{BASE_URL}/trsrv/secdef/search", params=params)
            
            if resp.ok:
                results = resp.json()
                if results:
                    option_info = {
                        'conid': results[0]['conid'],
                        'symbol': results[0]['symbol'],
                        'description': results[0].get('description', ''),
                        'strike': strike,
                        'expiry': expiry,
                        'right': right.upper(),
                        'currency': results[0].get('currency', 'USD'),
                        'exchange': results[0].get('exchange', '')
                    }
                    logger.info(f"Found option contract: {option_info}")
                    return option_info
                else:
                    logger.warning(f"No option contract found for {underlying_symbol} {expiry} ${strike} {right}")
                    return None
            else:
                logger.error(f"Failed to search for option: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error searching for specific option: {e}")
            return None
    
    def get_option_chain(self, underlying_symbol):
        """Get available option expirations for underlying"""
        try:
            underlying_conid = self.get_underlying_conid(underlying_symbol)
            if not underlying_conid:
                return None
            
            resp = self.session.get(f"{BASE_URL}/iserver/secdef/info", params={
                "conid": underlying_conid,
                "sectype": "OPT"
            })
            
            if resp.ok:
                data = resp.json()
                expirations = data.get('expirations', [])
                strikes = data.get('strikes', [])
                
                chain_info = {
                    'underlying_symbol': underlying_symbol,
                    'underlying_conid': underlying_conid,
                    'expirations': expirations,
                    'strikes': strikes,
                    'currency': data.get('currency', 'USD')
                }
                
                logger.info(f"Retrieved option chain for {underlying_symbol}: {len(expirations)} expirations, {len(strikes)} strikes")
                return chain_info
            else:
                logger.error(f"Failed to get option chain: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting option chain: {e}")
            return None
    
    def display_option_chain(self, chain_info):
        """Display option chain information in a formatted way"""
        if not chain_info:
            print("No option chain information available")
            return
        
        symbol = chain_info['underlying_symbol']
        expirations = chain_info['expirations']
        strikes = chain_info['strikes']
        
        print(f"\n===== Option Chain for {symbol} =====")
        print(f"Available Expirations ({len(expirations)}):")
        
        # Display expirations in rows of 5
        for i in range(0, len(expirations), 5):
            exp_row = expirations[i:i+5]
            print("  " + "  ".join(f"{exp:>10}" for exp in exp_row))
        
        print(f"\nAvailable Strikes ({len(strikes)}):")
        
        # Display strikes in rows of 10
        for i in range(0, len(strikes), 10):
            strike_row = strikes[i:i+10]
            print("  " + "  ".join(f"${strike:>6}" for strike in strike_row))
        
        print("=" * 50)
    
    def get_option_strikes_for_expiry(self, underlying_symbol, expiry):
        """Get available strikes for a specific expiration"""
        try:
            underlying_conid = self.get_underlying_conid(underlying_symbol)
            if not underlying_conid:
                return None
            
            resp = self.session.get(f"{BASE_URL}/iserver/secdef/strikes", params={
                "conid": underlying_conid,
                "sectype": "OPT",
                "month": expiry
            })
            
            if resp.ok:
                data = resp.json()
                strikes = data.get('strikes', [])
                logger.info(f"Found {len(strikes)} strikes for {underlying_symbol} expiry {expiry}")
                return strikes
            else:
                logger.error(f"Failed to get strikes for expiry: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting strikes for expiry: {e}")
            return None
