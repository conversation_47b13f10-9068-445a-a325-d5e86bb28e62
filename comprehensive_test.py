"""
Comprehensive Test Suite for Interactive Brokers Options Trading Application
Tests all functionalities from authentication to order placement
"""

import requests
import urllib3
import json
import time
from datetime import datetime, timedelta

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

class IBOptionsTestSuite:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = "https://localhost:5000/v1/api"
        self.account_id = None
        self.test_results = {}
        
    def log_test(self, test_name, success, message="", data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results[test_name] = {
            'success': success,
            'message': message,
            'data': data
        }
        
    def test_1_authentication(self):
        """Test 1: Authentication Status"""
        print("\n" + "="*60)
        print("TEST 1: AUTHENTICATION")
        print("="*60)
        
        try:
            resp = self.session.get(f"{self.base_url}/iserver/auth/status")
            if resp.ok:
                auth_data = resp.json()
                if auth_data.get('authenticated'):
                    server_info = auth_data.get('serverInfo', {})
                    self.log_test("Authentication", True, 
                                f"Authenticated to {server_info.get('serverName', 'Unknown')}")
                    return True
                else:
                    self.log_test("Authentication", False, "Not authenticated - please login at https://localhost:5000")
                    return False
            else:
                self.log_test("Authentication", False, f"HTTP {resp.status_code}")
                return False
        except Exception as e:
            self.log_test("Authentication", False, f"Connection error: {e}")
            return False
    
    def test_2_account_access(self):
        """Test 2: Account Access and Balances"""
        print("\n" + "="*60)
        print("TEST 2: ACCOUNT ACCESS & BALANCES")
        print("="*60)
        
        # Get accounts
        try:
            resp = self.session.get(f"{self.base_url}/portfolio/accounts")
            if resp.ok:
                accounts = resp.json()
                if accounts:
                    self.account_id = accounts[0]['id']
                    self.log_test("Account Discovery", True, f"Found account: {self.account_id}")
                else:
                    self.log_test("Account Discovery", False, "No accounts found")
                    return False
            else:
                self.log_test("Account Discovery", False, f"HTTP {resp.status_code}")
                return False
        except Exception as e:
            self.log_test("Account Discovery", False, f"Error: {e}")
            return False
        
        # Get account summary
        try:
            resp = self.session.get(f"{self.base_url}/iserver/account/{self.account_id}/summary")
            if resp.ok:
                summary = resp.json()
                balance = summary.get('balance', 0)
                buying_power = summary.get('buyingPower', 0)
                net_liq = summary.get('netLiquidationValue', 0)
                
                self.log_test("Account Balances", True, 
                            f"Cash: ${balance:,.2f}, Buying Power: ${buying_power:,.2f}")
                
                # Verify this is a paper trading account
                if self.account_id.startswith('DU'):
                    self.log_test("Paper Trading Verification", True, "Paper trading account confirmed")
                else:
                    self.log_test("Paper Trading Verification", False, "Not a paper trading account!")
                
                return True
            else:
                self.log_test("Account Balances", False, f"HTTP {resp.status_code}")
                return False
        except Exception as e:
            self.log_test("Account Balances", False, f"Error: {e}")
            return False
    
    def test_3_positions(self):
        """Test 3: Position Retrieval"""
        print("\n" + "="*60)
        print("TEST 3: POSITIONS")
        print("="*60)
        
        # Try multiple position endpoints
        endpoints = [
            f"/portfolio/{self.account_id}/positions",
            f"/iserver/account/positions/0",
            "/portfolio/positions"
        ]
        
        for endpoint in endpoints:
            try:
                resp = self.session.get(f"{self.base_url}{endpoint}")
                if resp.ok:
                    positions = resp.json()
                    self.log_test("Position Retrieval", True, 
                                f"Retrieved {len(positions)} positions from {endpoint}")
                    return True
                elif resp.status_code == 401:
                    continue  # Try next endpoint
                else:
                    self.log_test(f"Position Endpoint {endpoint}", False, f"HTTP {resp.status_code}")
            except Exception as e:
                self.log_test(f"Position Endpoint {endpoint}", False, f"Error: {e}")
        
        self.log_test("Position Retrieval", False, "All position endpoints failed - may need IB Gateway")
        return False
    
    def test_4_option_chain_search(self):
        """Test 4: Option Chain and Symbol Search"""
        print("\n" + "="*60)
        print("TEST 4: OPTION CHAIN & SYMBOL SEARCH")
        print("="*60)
        
        # Test popular symbols
        test_symbols = ["SPY", "AAPL", "QQQ", "TSLA"]
        
        for symbol in test_symbols:
            try:
                # Search for underlying stock
                resp = self.session.get(f"{self.base_url}/trsrv/secdef/search", params={
                    "symbol": symbol,
                    "secType": "STK"
                })
                
                if resp.ok:
                    results = resp.json()
                    if results:
                        conid = results[0]['conid']
                        self.log_test(f"Symbol Search - {symbol}", True, f"ConID: {conid}")
                        
                        # Try to get option chain
                        resp2 = self.session.get(f"{self.base_url}/iserver/secdef/info", params={
                            "conid": conid,
                            "sectype": "OPT"
                        })
                        
                        if resp2.ok:
                            chain_data = resp2.json()
                            expirations = chain_data.get('expirations', [])
                            strikes = chain_data.get('strikes', [])
                            
                            if expirations and strikes:
                                self.log_test(f"Option Chain - {symbol}", True, 
                                            f"{len(expirations)} expirations, {len(strikes)} strikes")
                                return True
                            else:
                                self.log_test(f"Option Chain - {symbol}", False, "No options data")
                        else:
                            self.log_test(f"Option Chain - {symbol}", False, f"HTTP {resp2.status_code}")
                    else:
                        self.log_test(f"Symbol Search - {symbol}", False, "No results")
                else:
                    self.log_test(f"Symbol Search - {symbol}", False, f"HTTP {resp.status_code}")
            except Exception as e:
                self.log_test(f"Symbol Search - {symbol}", False, f"Error: {e}")
        
        return False
    
    def test_5_market_data(self):
        """Test 5: Market Data Retrieval"""
        print("\n" + "="*60)
        print("TEST 5: MARKET DATA")
        print("="*60)
        
        # First find a valid contract
        try:
            resp = self.session.get(f"{self.base_url}/trsrv/secdef/search", params={
                "symbol": "SPY",
                "secType": "STK"
            })
            
            if resp.ok and resp.json():
                conid = resp.json()[0]['conid']
                
                # Get market data
                resp2 = self.session.get(f"{self.base_url}/iserver/marketdata/snapshot", params={
                    "conids": str(conid),
                    "fields": "31,84,86,87"  # Last, Bid, Ask, Volume
                })
                
                if resp2.ok:
                    market_data = resp2.json()
                    if market_data and len(market_data) > 0:
                        data = market_data[0]
                        last_price = data.get('31')
                        bid = data.get('84')
                        ask = data.get('86')
                        
                        self.log_test("Market Data", True, 
                                    f"SPY - Last: ${last_price}, Bid: ${bid}, Ask: ${ask}")
                        return True
                    else:
                        self.log_test("Market Data", False, "No market data returned")
                else:
                    self.log_test("Market Data", False, f"HTTP {resp2.status_code}")
            else:
                self.log_test("Market Data", False, "Could not find SPY contract")
        except Exception as e:
            self.log_test("Market Data", False, f"Error: {e}")
        
        return False
    
    def test_6_option_search_specific(self):
        """Test 6: Specific Option Contract Search"""
        print("\n" + "="*60)
        print("TEST 6: SPECIFIC OPTION SEARCH")
        print("="*60)
        
        # Calculate a reasonable expiry date (next Friday)
        today = datetime.now()
        days_ahead = 4 - today.weekday()  # Friday is 4
        if days_ahead <= 0:
            days_ahead += 7
        next_friday = today + timedelta(days=days_ahead + 7)  # Next week's Friday
        expiry = next_friday.strftime("%Y%m%d")
        
        # Test option search
        try:
            resp = self.session.get(f"{self.base_url}/trsrv/secdef/search", params={
                "symbol": "SPY",
                "secType": "OPT",
                "month": expiry,
                "strike": "580",
                "right": "C"
            })
            
            if resp.ok:
                results = resp.json()
                if results:
                    option_conid = results[0]['conid']
                    self.log_test("Option Contract Search", True, 
                                f"SPY {expiry} $580 Call - ConID: {option_conid}")
                    return True
                else:
                    self.log_test("Option Contract Search", False, "No option contracts found")
            else:
                self.log_test("Option Contract Search", False, f"HTTP {resp.status_code}")
        except Exception as e:
            self.log_test("Option Contract Search", False, f"Error: {e}")
        
        return False
    
    def test_7_order_validation(self):
        """Test 7: Order Validation (WhatIf)"""
        print("\n" + "="*60)
        print("TEST 7: ORDER VALIDATION")
        print("="*60)
        
        # First find a valid option contract
        try:
            resp = self.session.get(f"{self.base_url}/trsrv/secdef/search", params={
                "symbol": "SPY",
                "secType": "STK"
            })
            
            if resp.ok and resp.json():
                conid = resp.json()[0]['conid']
                
                # Create a test order for validation
                order_data = {
                    "conid": str(conid),
                    "orderType": "MKT",
                    "side": "BUY",
                    "quantity": 1,
                    "tif": "DAY"
                }
                
                # Validate order
                resp2 = self.session.post(
                    f"{self.base_url}/iserver/account/orders/whatif",
                    data=json.dumps(order_data),
                    headers={"Content-Type": "application/json"}
                )
                
                if resp2.ok:
                    validation = resp2.json()
                    self.log_test("Order Validation", True, "Order validation successful")
                    return True
                else:
                    self.log_test("Order Validation", False, f"HTTP {resp2.status_code}")
            else:
                self.log_test("Order Validation", False, "Could not find contract for validation")
        except Exception as e:
            self.log_test("Order Validation", False, f"Error: {e}")
        
        return False
    
    def test_8_order_status(self):
        """Test 8: Order Status Retrieval"""
        print("\n" + "="*60)
        print("TEST 8: ORDER STATUS")
        print("="*60)
        
        try:
            resp = self.session.get(f"{self.base_url}/iserver/account/orders")
            if resp.ok:
                orders = resp.json()
                self.log_test("Order Status Retrieval", True, f"Retrieved {len(orders)} orders")
                
                # Check for options orders
                options_orders = [o for o in orders if o.get('assetClass') == 'OPT']
                self.log_test("Options Orders", True, f"Found {len(options_orders)} options orders")
                return True
            else:
                self.log_test("Order Status Retrieval", False, f"HTTP {resp.status_code}")
        except Exception as e:
            self.log_test("Order Status Retrieval", False, f"Error: {e}")
        
        return False
    
    def run_all_tests(self):
        """Run all tests in sequence"""
        print("🚀 Starting Comprehensive Test Suite for IB Options Trading")
        print("=" * 80)
        
        start_time = time.time()
        
        # Run tests in order
        tests = [
            self.test_1_authentication,
            self.test_2_account_access,
            self.test_3_positions,
            self.test_4_option_chain_search,
            self.test_5_market_data,
            self.test_6_option_search_specific,
            self.test_7_order_validation,
            self.test_8_order_status
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "="*80)
        print("🏁 TEST SUITE SUMMARY")
        print("="*80)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if passed >= 3:  # At least basic functionality works
            print("\n✅ CORE FUNCTIONALITY IS WORKING!")
            print("Your Interactive Brokers Options Trading application is functional.")
        else:
            print("\n❌ CRITICAL ISSUES DETECTED")
            print("Please check your setup and authentication.")
        
        # Detailed results
        print("\n📊 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            status = "✅" if result['success'] else "❌"
            print(f"{status} {test_name}: {result['message']}")
        
        return passed >= 3

if __name__ == "__main__":
    test_suite = IBOptionsTestSuite()
    test_suite.run_all_tests()
