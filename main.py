"""
Main application for Interactive Brokers Options Trading
"""

import logging
import sys
from auth import I<PERSON>uth
from account import Account<PERSON><PERSON>ger
from positions import PositionManager
from options_search import OptionsSearch
from market_data import MarketDataManager
from orders import OrderManager
from config import LOG_LEVEL, LOG_FORMAT

# Configure logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class OptionsTrader:
    def __init__(self):
        self.auth = IBAuth()
        self.session = None
        self.account_manager = None
        self.position_manager = None
        self.options_search = None
        self.market_data_manager = None
        self.order_manager = None
        
    def initialize(self):
        """Initialize the trading application"""
        print("Initializing Interactive Brokers Options Trading Application...")
        
        # Check authentication
        if not self.auth.is_authenticated():
            print("\n❌ Not authenticated with Interactive Brokers")
            print("Please login via browser at: https://localhost:5000")
            print("Then restart this application.")
            return False
        
        print("✅ Authentication successful")
        
        # Get authenticated session
        self.session = self.auth.get_session()
        if not self.session:
            print("❌ Failed to get authenticated session")
            return False
        
        # Initialize managers
        self.account_manager = AccountManager(self.session)
        self.position_manager = PositionManager(self.session)
        self.options_search = OptionsSearch(self.session)
        self.market_data_manager = MarketDataManager(self.session)
        self.order_manager = OrderManager(self.session)
        
        print("✅ Application initialized successfully")
        return True
    
    def display_menu(self):
        """Display the main menu options"""
        print("\n===== Interactive Brokers Options Trading Menu =====")
        print("1. Fetch Account Balances")
        print("2. Fetch Open Option Positions")
        print("3. Fetch Option Chain for Symbol")
        print("4. Get Option Market Data")
        print("5. Place Option Order")
        print("6. Check Order Status")
        print("7. Cancel Order")
        print("8. Exit")
        print("=================================================")
        return input("Enter your choice (1-8): ")
    
    def fetch_account_balances(self):
        """Fetch and display account balances"""
        print("\nFetching account balances...")
        balances = self.account_manager.get_account_balances()
        self.account_manager.display_account_balances(balances)
    
    def fetch_option_positions(self):
        """Fetch and display option positions"""
        print("\nFetching option positions...")
        positions = self.position_manager.get_options_positions()
        self.position_manager.display_options_positions(positions)
    
    def fetch_option_chain(self):
        """Fetch and display option chain for a symbol"""
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper().strip()
        if not symbol:
            print("Invalid symbol")
            return
        
        print(f"\nFetching option chain for {symbol}...")
        chain_info = self.options_search.get_option_chain(symbol)
        self.options_search.display_option_chain(chain_info)
    
    def get_option_market_data(self):
        """Get and display option market data"""
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper().strip()
        expiry = input("Enter expiry date (YYYYMMDD): ").strip()
        strike = input("Enter strike price: ").strip()
        right = input("Enter option type (C for Call, P for Put): ").upper().strip()
        
        if not all([symbol, expiry, strike, right]):
            print("All fields are required")
            return
        
        try:
            strike = float(strike)
        except ValueError:
            print("Invalid strike price")
            return
        
        if right not in ['C', 'P']:
            print("Option type must be C or P")
            return
        
        print(f"\nFetching market data for {symbol} {expiry} ${strike} {right}...")
        
        # First find the option contract
        option_info = self.options_search.search_specific_option(symbol, expiry, strike, right)
        if not option_info:
            print("Option contract not found")
            return
        
        # Get market data
        market_data = self.market_data_manager.get_option_market_data(option_info['conid'])
        self.market_data_manager.display_option_market_data(market_data, option_info)
    
    def place_option_order(self):
        """Place an option order"""
        print("\n===== Place Option Order =====")
        
        # Get contract details
        symbol = input("Enter underlying symbol (e.g., SPY): ").upper().strip()
        expiry = input("Enter expiry date (YYYYMMDD): ").strip()
        strike = input("Enter strike price: ").strip()
        right = input("Enter option type (C for Call, P for Put): ").upper().strip()
        
        if not all([symbol, expiry, strike, right]):
            print("All fields are required")
            return
        
        try:
            strike = float(strike)
        except ValueError:
            print("Invalid strike price")
            return
        
        if right not in ['C', 'P']:
            print("Option type must be C or P")
            return
        
        # Find the option contract
        option_info = self.options_search.search_specific_option(symbol, expiry, strike, right)
        if not option_info:
            print("Option contract not found")
            return
        
        # Get order details
        side = input("Enter side (BUY/SELL): ").upper().strip()
        if side not in ['BUY', 'SELL']:
            print("Side must be BUY or SELL")
            return
        
        try:
            quantity = int(input("Enter quantity: ").strip())
        except ValueError:
            print("Invalid quantity")
            return
        
        order_type = input("Enter order type (MARKET/LIMIT): ").upper().strip()
        if order_type not in ['MARKET', 'LIMIT']:
            print("Order type must be MARKET or LIMIT")
            return
        
        limit_price = None
        if order_type == 'LIMIT':
            try:
                limit_price = float(input("Enter limit price: ").strip())
            except ValueError:
                print("Invalid limit price")
                return
        
        # Place the order
        print(f"\nPlacing {order_type} order: {side} {quantity} {symbol} {expiry} ${strike} {right}")
        
        if order_type == 'MARKET':
            result = self.order_manager.place_market_order(option_info['conid'], side, quantity)
        else:
            result = self.order_manager.place_limit_order(option_info['conid'], side, quantity, limit_price)
        
        if result:
            print("✅ Order placed successfully")
            print(f"Order details: {result}")
        else:
            print("❌ Failed to place order")
    
    def check_order_status(self):
        """Check and display order status"""
        print("\nFetching order status...")
        orders = self.order_manager.get_options_orders()
        self.order_manager.display_orders(orders)
    
    def cancel_order(self):
        """Cancel an order"""
        # First show current orders
        orders = self.order_manager.get_options_orders()
        if not orders:
            print("No orders to cancel")
            return
        
        self.order_manager.display_orders(orders)
        
        order_id = input("\nEnter Order ID to cancel: ").strip()
        if not order_id:
            print("Invalid Order ID")
            return
        
        print(f"Cancelling order {order_id}...")
        result = self.order_manager.cancel_order(order_id)
        
        if result:
            print("✅ Order cancelled successfully")
        else:
            print("❌ Failed to cancel order")
    
    def run(self):
        """Run the main application loop"""
        if not self.initialize():
            return
        
        print("\n🚀 Welcome to Interactive Brokers Options Trading Application!")
        
        while True:
            try:
                choice = self.display_menu()
                
                if choice == '1':
                    self.fetch_account_balances()
                elif choice == '2':
                    self.fetch_option_positions()
                elif choice == '3':
                    self.fetch_option_chain()
                elif choice == '4':
                    self.get_option_market_data()
                elif choice == '5':
                    self.place_option_order()
                elif choice == '6':
                    self.check_order_status()
                elif choice == '7':
                    self.cancel_order()
                elif choice == '8':
                    print("\nThank you for using IB Options Trading Application!")
                    break
                else:
                    print("Invalid choice. Please enter 1-8.")
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\nApplication interrupted by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                print(f"An error occurred: {e}")

if __name__ == "__main__":
    trader = OptionsTrader()
    trader.run()
