-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/reg-am/login.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/reg-am/login.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /sso/lib/xyz.bundle.min.js
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
<- 200 GET /sso/lib/xyz.bundle.min.js
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 204 POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
-> GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
<- 200 GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /images/web/favicons/home-screen-icon-128x128.png
<- 200 GET /images/web/favicons/home-screen-icon-128x128.png
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 302 POST /sso/Dispatcher
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|608ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|401ms
-> request: /v1/api/ssodh/st
-> sso GET https://api.ibkr.com/v1/api/ssodh/st
-> request: /sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=40f699c5|74-13-EA-C1-94-7C
-> sso GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=40f699c5|74-13-EA-C1-94-7C
<- 200 GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=40f699c5|74-13-EA-C1-94-7C 0
-> request: /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status,200|204ms
-> request: /v1/api/iserver/auth/ssodh/init
-> POST https://api.ibkr.com/v1/api/iserver/auth/ssodh/init,200|2504ms
-> request: /v1/api/iserver/accounts
-> GET /v1/api/iserver/accounts,200|196ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|218ms
-> GET /v1/api/iserver/account
<- 405 GET /v1/api/iserver/account
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|357ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/account
<- 405 GET /v1/api/iserver/account
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|176ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|204ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|182ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|372ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|184ms
-> GET /v1/api/iserver/auth/status
<- 401 GET /v1/api/iserver/auth/status
-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/reg-am/login.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /css/reg-am/login.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /sso/lib/xyz.bundle.min.js
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /sso/lib/xyz.bundle.min.js
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 204 POST /portal.proxy/v1/gstat/bulletins
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /images/web/favicons/home-screen-icon-192x192.png
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /v1/api/iserver/auth/status
<- 401 GET /v1/api/iserver/auth/status
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> GET /v1/api/iserver/auth/status
<- 401 GET /v1/api/iserver/auth/status
-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /css/reg-am/login.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
-> GET /sso/lib/xyz.bundle.min.js
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
<- 200 GET /css/reg-am/login.min.css
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /sso/lib/xyz.bundle.min.js
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
<- 200 GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /sso/images/2fa-animated-once.gif
<- 204 POST /portal.proxy/v1/gstat/bulletins
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 302 POST /sso/Dispatcher
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|778ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|576ms
-> request: /v1/api/ssodh/st
-> sso GET https://api.ibkr.com/v1/api/ssodh/st
-> request: /sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=3a73f42e|74-13-EA-C1-94-7C
-> sso GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=3a73f42e|74-13-EA-C1-94-7C
<- 200 GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=3a73f42e|74-13-EA-C1-94-7C 0
-> request: /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status,200|573ms
-> request: /v1/api/iserver/auth/ssodh/init
-> POST https://api.ibkr.com/v1/api/iserver/auth/ssodh/init,200|2887ms
-> request: /v1/api/iserver/accounts
-> GET /v1/api/iserver/accounts,200|588ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|816ms
-> request: /v1/api/sso/validate?gw=1
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|577ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/sso/validate?gw=1,200|672ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|558ms
-> GET /v1/api/one/user,200|566ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/account
<- 405 GET /v1/api/iserver/account
-> GET /v1/api/iserver/accounts
<- 200 GET /v1/api/iserver/accounts
-> GET /v1/api/iserver/account/DUM602021/summary
<- 200 GET /v1/api/iserver/account/DUM602021/summary
-> GET /v1/api/portfolio/accounts
<- 200 GET /v1/api/portfolio/accounts
-> GET /v1/api/portfolio/DUM602021/positions/0
<- 401 GET /v1/api/portfolio/DUM602021/positions/0
-> GET /v1/api/portfolio/positions
<- 404 GET /v1/api/portfolio/positions
-> GET /v1/api/portfolio/DUM602021/summary
<- 401 GET /v1/api/portfolio/DUM602021/summary
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/portfolio/accounts
<- 200 GET /v1/api/portfolio/accounts
-> GET /v1/api/iserver/account/DUM602021/summary
<- 200 GET /v1/api/iserver/account/DUM602021/summary
-> request: /sso/ping
-> GET /sso/ping,200|981ms
-> GET /v1/api/iserver/account/DUM602021/summary
<- 200 GET /v1/api/iserver/account/DUM602021/summary
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/portfolio/accounts
<- 200 GET /v1/api/portfolio/accounts
-> GET /v1/api/iserver/account/DUM602021/summary
<- 200 GET /v1/api/iserver/account/DUM602021/summary
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/portfolio/accounts
<- 200 GET /v1/api/portfolio/accounts
-> GET /v1/api/iserver/account/DUM602021/summary
<- 200 GET /v1/api/iserver/account/DUM602021/summary
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/portfolio/accounts
<- 200 GET /v1/api/portfolio/accounts
-> GET /v1/api/iserver/account/DUM602021/summary
<- 200 GET /v1/api/iserver/account/DUM602021/summary
-> GET /v1/api/portfolio/0/positions
<- 401 GET /v1/api/portfolio/0/positions
-> GET /v1/api/iserver/account/positions/0
<- 404 GET /v1/api/iserver/account/positions/0
-> GET /v1/api/portfolio/positions
<- 404 GET /v1/api/portfolio/positions
-> GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=600.0&right=C
<- 404 GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=600.0&right=C
-> GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=60.0&right=C
<- 404 GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=60.0&right=C
-> request: /sso/ping
-> GET /sso/ping,200|956ms
-> GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=600.0&right=C
<- 404 GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=600.0&right=C
-> request: /sso/ping
-> GET /sso/ping,200|779ms
-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/reg-am/login.min.css
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/reg-am/login.min.css
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /sso/lib/xyz.bundle.min.js
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /sso/lib/xyz.bundle.min.js
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
<- 204 POST /portal.proxy/v1/gstat/bulletins
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
-> GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/ibkr-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
<- 200 GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
<- 200 GET /sso/images/2fa-animated-once.gif
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 302 POST /sso/Dispatcher
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|475ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|461ms
-> request: /v1/api/ssodh/st
-> sso GET https://api.ibkr.com/v1/api/ssodh/st
-> request: /sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=29a99d9d|74-13-EA-C1-94-7C
-> sso GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=29a99d9d|74-13-EA-C1-94-7C
<- 200 GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=29a99d9d|74-13-EA-C1-94-7C 0
-> request: /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status,200|241ms
-> request: /v1/api/iserver/auth/ssodh/init
-> POST https://api.ibkr.com/v1/api/iserver/auth/ssodh/init,200|2553ms
-> request: /v1/api/iserver/accounts
-> GET /v1/api/iserver/accounts,200|255ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|287ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|461ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|304ms
-> GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=600.0&right=C
<- 404 GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=OPT&month=********&strike=600.0&right=C
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|598ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|288ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|423ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|277ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|290ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|262ms
-> request: /sso/ping
-> GET /sso/ping,200|645ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|494ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|240ms
-> GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=STK
<- 404 GET /v1/api/trsrv/secdef/search?symbol=SPY&secType=STK
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|448ms
-> GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
<- 200 GET /sso/Login?forwardTo=22&RL=1&ip2loc=US
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/reg-am/login.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/reg-am/login.min.css
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
-> GET /sso/lib/xyz.bundle.min.js
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
<- 200 GET /sso/lib/xyz.bundle.min.js
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 204 POST /portal.proxy/v1/gstat/bulletins
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
-> GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /images/common/logos/ibkr/ibkr-inverse.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant.svg
-> GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
<- 200 GET /images/common/logos/ibkr/ibkr.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhant-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans.svg
-> GET /images/web/favicons/home-screen-icon-192x192.png
-> GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/common/logos/ibkr/ibkr-inverse.svg
<- 200 GET /images/common/logos/ibkr/interactive-brokers-zhhans-inverse.svg
<- 200 GET /sso/images/2fa-animated-once.gif
<- 200 GET /images/web/favicons/home-screen-icon-192x192.png
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 200 POST /sso/Dispatcher
-> GET /css/bootstrap-5.2.2/bootstrap.min.css
-> GET /css/reg-am/login.min.css
-> GET /css/fontawesome-6.2.0/all.min.css
-> GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /css/ibkr/theme-ibkr-portal.min.css
<- 200 GET /css/bootstrap-switch-3.3.2/bootstrap-switch.min.css
-> GET /scripts/common/js/jquery-3.7.0/jquery.min.js
-> GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /css/bootstrap-5.2.2/bootstrap.min.css
<- 200 GET /css/reg-am/login.min.css
<- 200 GET /css/fontawesome-6.2.0/all.min.css
<- 200 GET /scripts/common/js/bootstrap-5.2.2/bootstrap.bundle.min.js
<- 200 GET /scripts/common/js/jquery-3.7.0/jquery.min.js
-> GET /sso/lib/xyz.bundle.min.js
<- 200 GET /sso/lib/xyz.bundle.min.js
-> GET /images/common/logos/ibkr/interactive-brokers.svg
-> GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
-> GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
-> GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
-> POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /images/common/logos/ibkr/interactive-brokers.svg
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Semibold.woff2
<- 200 GET /fonts/proxima-nova/Proxima-Nova-Regular.woff2
<- 200 GET /fonts/fontawesome-6.2.0/webfonts/fa-brands-400.woff2
<- 200 GET /fonts/fontawesome-6.4.2/webfonts/fa-solid-900.woff2
-> GET /images/web/favicons/home-screen-icon-192x192.png
-> GET /sso/images/2fa-animated-once.gif
<- 204 POST /portal.proxy/v1/gstat/bulletins
<- 200 GET /images/web/favicons/home-screen-icon-192x192.png
<- 200 GET /sso/images/2fa-animated-once.gif
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Authenticator
<- 200 POST /sso/Authenticator
-> POST /sso/Dispatcher
<- 302 POST /sso/Dispatcher
-> request: /v1/api/sso/validate?gw=1
-> GET /v1/api/sso/validate?gw=1,200|429ms
-> request: /v1/api/one/user
-> request: /v1/api/ssodh/init
-> sso GET https://api.ibkr.com/v1/api/ssodh/init
-> GET /v1/api/one/user,200|194ms
-> request: /v1/api/ssodh/st
-> sso GET https://api.ibkr.com/v1/api/ssodh/st
-> request: /sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=17f282f1|74-13-EA-C1-94-7C
-> sso GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=17f282f1|74-13-EA-C1-94-7C
<- 200 GET https://api.ibkr.com/sso/Authenticator?ACTION=PUBLISH_TST&RESP_TYPE=JSON&DEVICE_ID=17f282f1|74-13-EA-C1-94-7C 0
-> request: /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status,200|175ms
-> request: /v1/api/iserver/accounts
-> GET /v1/api/iserver/accounts,200|176ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|189ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|191ms
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> GET /v1/api/iserver/auth/status
<- 200 GET /v1/api/iserver/auth/status
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|178ms
-> request: /v1/api/tickle
-> GET /v1/api/tickle,200|193ms
-> GET /v1/api/iserver/auth/status
<- 401 GET /v1/api/iserver/auth/status
