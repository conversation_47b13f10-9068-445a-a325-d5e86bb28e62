"""
Interactive Brokers Options Trading - Implementation based on Reference Guide
Uses exact endpoints and patterns from the provided reference documentation
"""

import requests
import json
import urllib3
from datetime import datetime

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

# Configuration from reference guide
BASE_URL = "https://localhost:5000/v1/api"
SESSION = requests.Session()
SESSION.verify = False  # IB uses self-signed cert

class IBOptionsTrader:
    def __init__(self):
        self.session = SESSION
        self.authenticated = False
        
    def check_auth_status(self):
        """Check authentication status"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/auth/status")
            if resp.ok:
                auth_data = resp.json()
                self.authenticated = auth_data.get('authenticated', False)
                print("Auth Status:", auth_data)
                return resp.ok
            else:
                print(f"Auth check failed: {resp.status_code}")
                return False
        except Exception as e:
            print(f"Auth error: {e}")
            return False

    def get_options_account_balances(self):
        """Fetch current account balances relevant to Options trading"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/account")
            accounts = resp.json()
            
            if accounts:
                account_id = accounts[0]['id']
                balance_resp = self.session.get(f"{BASE_URL}/iserver/account/{account_id}/summary")
                balances = balance_resp.json()
                
                print("Account Balances for Options Trading:")
                for item in balances:
                    key = item.get('key')
                    if key in ['TotalCashValue', 'AvailableFunds', 'BuyingPower', 'OptionMarketValue']:
                        print(f"  {key}: {item.get('value')} {item.get('currency', 'USD')}")
                
                return balances
            return None
        except Exception as e:
            print(f"Error getting balances: {e}")
            return None

    def get_options_positions(self):
        """Fetch current open Options positions"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/account/positions/0")
            positions = resp.json()
            
            options_positions = []
            for position in positions:
                if position.get('assetClass') == 'OPT':
                    options_positions.append({
                        'symbol': position.get('ticker'),
                        'position': position.get('position'),
                        'market_price': position.get('mktPrice'),
                        'unrealized_pnl': position.get('unrealizedPnl'),
                        'expiry': position.get('expiry'),
                        'strike': position.get('strike'),
                        'right': position.get('right'),  # 'C' for Call, 'P' for Put
                    })
            
            print("Current Options Positions:")
            for pos in options_positions:
                option_type = "Call" if pos['right'] == 'C' else "Put"
                print(f"  {pos['symbol']} {pos['expiry']} ${pos['strike']} {option_type}: {pos['position']} contracts")
            
            return options_positions
        except Exception as e:
            print(f"Error getting positions: {e}")
            return []

    def get_underlying_conid(self, symbol):
        """Get ConID for underlying stock to fetch option chain"""
        try:
            resp = self.session.get(f"{BASE_URL}/trsrv/secdef/search", params={
                "symbol": symbol,
                "secType": "STK"
            })
            
            results = resp.json()
            if results:
                return results[0]['conid']
            return None
        except Exception as e:
            print(f"Error getting underlying ConID: {e}")
            return None

    def search_specific_option(self, underlying_symbol, expiry, strike, right):
        """Search for a specific option contract"""
        try:
            resp = self.session.get(f"{BASE_URL}/trsrv/secdef/search", params={
                "symbol": underlying_symbol,
                "secType": "OPT",
                "month": expiry,  # Format: YYYYMMDD
                "strike": strike,
                "right": right.upper()  # 'C' for Call, 'P' for Put
            })
            
            results = resp.json()
            print(f"Option search for {underlying_symbol} {expiry} ${strike} {right}:", results)
            
            if results:
                return {
                    'conid': results[0]['conid'],
                    'symbol': results[0]['symbol'],
                    'strike': strike,
                    'expiry': expiry,
                    'right': right
                }
            return None
        except Exception as e:
            print(f"Error searching option: {e}")
            return None

    def get_option_chain(self, underlying_symbol):
        """Get available option expirations for underlying"""
        try:
            underlying_conid = self.get_underlying_conid(underlying_symbol)
            if not underlying_conid:
                return None
            
            resp = self.session.get(f"{BASE_URL}/iserver/secdef/info", params={
                "conid": underlying_conid, 
                "sectype": "OPT"
            })
            
            if resp.ok:
                data = resp.json()
                expirations = data.get('expirations', [])
                print(f"Available option expirations for {underlying_symbol}: {expirations}")
                return expirations
            return None
        except Exception as e:
            print(f"Error getting option chain: {e}")
            return None

    def get_option_market_data(self, underlying_symbol, expiry, strike, right):
        """Get real-time market data for a specific option contract"""
        try:
            option_info = self.search_specific_option(underlying_symbol, expiry, strike, right)
            
            if not option_info:
                return None
            
            conid = str(option_info['conid'])
            resp = self.session.get(f"{BASE_URL}/iserver/marketdata/snapshot", params={
                "conids": conid,
                "fields": "31,84,86,87,7295,7296,7308,7309"  # Price, volume, and Greeks
            })
            
            market_data = resp.json()
            if market_data:
                data = market_data[0]
                print(f"Option Market Data for {underlying_symbol} {expiry} ${strike} {right}:")
                print(f"  Last Price: ${data.get('31', 'N/A')}")
                print(f"  Bid: ${data.get('84', 'N/A')}")
                print(f"  Ask: ${data.get('86', 'N/A')}")
                print(f"  Volume: {data.get('87', 'N/A')}")
                print(f"  Delta: {data.get('7308', 'N/A')}")
                print(f"  Theta: {data.get('7295', 'N/A')}")
            
            return market_data
        except Exception as e:
            print(f"Error getting market data: {e}")
            return None

    def place_option_market_order(self, underlying_symbol, expiry, strike, right, side, quantity):
        """Place a market order for single-leg options trading"""
        try:
            option_info = self.search_specific_option(underlying_symbol, expiry, strike, right)
            
            if not option_info:
                print(f"Could not find option contract: {underlying_symbol} {expiry} ${strike} {right}")
                return None
            
            order_data = {
                "conid": str(option_info['conid']),
                "orderType": "MKT",
                "side": side.upper(),  # BUY or SELL
                "quantity": quantity,
                "tif": "DAY",
                "useAdaptive": False,
                "outsideRTH": False
            }
            
            # Validate order first
            validate = self.session.post(
                f"{BASE_URL}/iserver/account/orders/whatif",
                data=json.dumps(order_data),
                headers={"Content-Type": "application/json"}
            )
            print("Option Order Validation:", validate.json())
            
            # Place the order
            place = self.session.post(
                f"{BASE_URL}/iserver/account/orders",
                data=json.dumps([order_data]),
                headers={"Content-Type": "application/json"}
            )
            print("Option Market Order Response:", place.json())
            return place.json()
        except Exception as e:
            print(f"Error placing market order: {e}")
            return None

    def place_option_limit_order(self, underlying_symbol, expiry, strike, right, side, quantity, limit_price):
        """Place a limit order for single-leg options trading"""
        try:
            option_info = self.search_specific_option(underlying_symbol, expiry, strike, right)
            
            if not option_info:
                print(f"Could not find option contract: {underlying_symbol} {expiry} ${strike} {right}")
                return None
            
            order_data = {
                "conid": str(option_info['conid']),
                "orderType": "LMT",
                "side": side.upper(),
                "quantity": quantity,
                "price": limit_price,
                "tif": "DAY",
                "useAdaptive": False,
                "outsideRTH": False
            }
            
            # Validate and place order
            validate = self.session.post(
                f"{BASE_URL}/iserver/account/orders/whatif",
                data=json.dumps(order_data),
                headers={"Content-Type": "application/json"}
            )
            print("Option Limit Order Validation:", validate.json())
            
            place = self.session.post(
                f"{BASE_URL}/iserver/account/orders",
                data=json.dumps([order_data]),
                headers={"Content-Type": "application/json"}
            )
            print("Option Limit Order Response:", place.json())
            return place.json()
        except Exception as e:
            print(f"Error placing limit order: {e}")
            return None

    def get_options_order_status(self):
        """Fetch the status of existing Options orders"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/account/orders")
            orders = resp.json()
            
            options_orders = []
            for order in orders:
                if order.get('assetClass') == 'OPT':
                    options_orders.append({
                        'order_id': order.get('orderId'),
                        'symbol': order.get('ticker'),
                        'side': order.get('side'),
                        'quantity': order.get('totalSize'),
                        'status': order.get('status'),
                        'filled': order.get('filledQuantity', 0),
                        'avg_price': order.get('avgPrice'),
                        'expiry': order.get('expiry'),
                        'strike': order.get('strike'),
                        'right': order.get('right')
                    })
            
            print("Options Order Status:")
            for order in options_orders:
                option_type = "Call" if order['right'] == 'C' else "Put"
                print(f"  Order {order['order_id']}: {order['side']} {order['quantity']} {order['symbol']} {option_type}")
                print(f"    Status: {order['status']}, Filled: {order['filled']}")
            
            return options_orders
        except Exception as e:
            print(f"Error getting order status: {e}")
            return []

    def run_interactive_menu(self):
        """Run interactive menu for testing all functionalities"""
        if not self.check_auth_status():
            print("❌ Not authenticated. Please login at https://localhost:5000")
            return
        
        while True:
            print("\n===== IB Options Trading (Reference Implementation) =====")
            print("1. Check Authentication")
            print("2. Get Account Balances")
            print("3. Get Options Positions")
            print("4. Get Option Chain (SPY)")
            print("5. Get Option Market Data")
            print("6. Place Option Order")
            print("7. Check Order Status")
            print("8. Exit")
            print("=" * 55)
            
            choice = input("Enter choice (1-8): ").strip()
            
            if choice == '1':
                self.check_auth_status()
            elif choice == '2':
                self.get_options_account_balances()
            elif choice == '3':
                self.get_options_positions()
            elif choice == '4':
                self.get_option_chain("SPY")
            elif choice == '5':
                self.get_option_market_data("SPY", "********", 580, "C")
            elif choice == '6':
                print("Testing option order placement...")
                self.place_option_limit_order("SPY", "********", 580, "C", "BUY", 1, 5.00)
            elif choice == '7':
                self.get_options_order_status()
            elif choice == '8':
                break
            else:
                print("Invalid choice")
            
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    trader = IBOptionsTrader()
    trader.run_interactive_menu()
