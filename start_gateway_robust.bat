@echo off
title IB Client Portal Gateway
color 0A

echo ================================================================
echo  Interactive Brokers Client Portal Gateway
echo ================================================================
echo.
echo Starting gateway...
echo.
echo IMPORTANT: After the gateway starts:
echo 1. Go to: https://localhost:5000
echo 2. Login with your PAPER TRADING credentials
echo 3. Wait for "Client login succeeds" message
echo 4. Keep this window open while trading
echo.
echo Press Ctrl+C to stop the gateway
echo ================================================================
echo.

:START
.\bin\run.bat root\conf.yaml
echo.
echo Gateway stopped. Restarting in 5 seconds...
echo Press Ctrl+C to exit or wait to restart...
timeout /t 5 /nobreak > nul
goto START
