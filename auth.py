"""
Authentication module for Interactive Brokers API
"""

import requests
import logging
from config import BASE_URL, VERIFY_SSL

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IBAuth:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = VERIFY_SSL
        self.authenticated = False
        
    def check_auth_status(self):
        """Check current authentication status"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/auth/status")
            if resp.ok:
                auth_data = resp.json()
                self.authenticated = auth_data.get('authenticated', False)
                logger.info(f"Authentication Status: {auth_data}")
                return auth_data
            else:
                logger.error(f"Failed to check auth status: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error checking auth status: {e}")
            return None
    
    def is_authenticated(self):
        """Check if currently authenticated"""
        auth_status = self.check_auth_status()
        return auth_status and auth_status.get('authenticated', False)
    
    def reauthenticate(self):
        """Attempt to reauthenticate"""
        try:
            resp = self.session.post(f"{BASE_URL}/iserver/reauthenticate")
            if resp.ok:
                logger.info("Reauthentication successful")
                return True
            else:
                logger.error(f"Reauthentication failed: {resp.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error during reauthentication: {e}")
            return False
    
    def logout(self):
        """Logout from the session"""
        try:
            resp = self.session.post(f"{BASE_URL}/logout")
            if resp.ok:
                self.authenticated = False
                logger.info("Logout successful")
                return True
            else:
                logger.error(f"Logout failed: {resp.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return False
    
    def get_session(self):
        """Get the authenticated session"""
        if not self.is_authenticated():
            logger.warning("Session not authenticated. Please login via browser at https://localhost:5000")
            return None
        return self.session
