"""
Helper script to find valid option contracts for testing
"""

import requests
import urllib3
from datetime import datetime, timedelta

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

def find_valid_spy_options():
    """Find valid SPY option contracts"""
    print("🔍 Finding Valid SPY Option Contracts")
    print("=" * 50)
    
    session = requests.Session()
    session.verify = False
    base_url = "https://localhost:5000/v1/api"
    
    # First get SPY underlying ConID
    print("1. Getting SPY underlying contract...")
    resp = session.get(f"{base_url}/trsrv/secdef/search", params={
        "symbol": "SPY",
        "secType": "STK"
    })
    
    if not resp.ok:
        print(f"❌ Failed to get SPY contract: {resp.status_code}")
        return
    
    results = resp.json()
    if not results:
        print("❌ No SPY contract found")
        return
    
    spy_conid = results[0]['conid']
    print(f"✅ SPY ConID: {spy_conid}")
    
    # Get option chain
    print("\n2. Getting SPY option chain...")
    resp = session.get(f"{base_url}/iserver/secdef/info", params={
        "conid": spy_conid,
        "sectype": "OPT"
    })
    
    if not resp.ok:
        print(f"❌ Failed to get option chain: {resp.status_code}")
        return
    
    chain_data = resp.json()
    expirations = chain_data.get('expirations', [])
    strikes = chain_data.get('strikes', [])
    
    print(f"✅ Found {len(expirations)} expirations and {len(strikes)} strikes")
    
    # Show available expirations
    print("\n📅 Available Expirations (first 10):")
    for i, exp in enumerate(expirations[:10]):
        # Convert to readable date
        try:
            date_obj = datetime.strptime(exp, "%Y%m%d")
            readable = date_obj.strftime("%Y-%m-%d (%A)")
            print(f"   {i+1:2d}. {exp} ({readable})")
        except:
            print(f"   {i+1:2d}. {exp}")
    
    # Show available strikes (middle range)
    print(f"\n💰 Available Strikes (middle range of {len(strikes)} total):")
    mid_point = len(strikes) // 2
    start_idx = max(0, mid_point - 10)
    end_idx = min(len(strikes), mid_point + 10)
    
    for i, strike in enumerate(strikes[start_idx:end_idx]):
        print(f"   ${strike}")
    
    # Test a few specific option contracts
    print("\n🧪 Testing Specific Option Contracts:")
    
    # Use the first available expiration and a middle strike
    if expirations and strikes:
        test_expiry = expirations[0]
        test_strike = strikes[len(strikes)//2]
        
        test_cases = [
            (test_expiry, test_strike, "C"),
            (test_expiry, test_strike, "P"),
        ]
        
        for expiry, strike, right in test_cases:
            option_type = "Call" if right == "C" else "Put"
            print(f"\n   Testing: SPY {expiry} ${strike} {option_type}")
            
            resp = session.get(f"{base_url}/trsrv/secdef/search", params={
                "symbol": "SPY",
                "secType": "OPT",
                "month": expiry,
                "strike": str(strike),
                "right": right
            })
            
            if resp.ok:
                results = resp.json()
                if results:
                    conid = results[0]['conid']
                    print(f"   ✅ Found: ConID {conid}")
                    
                    # Test market data for this contract
                    resp2 = session.get(f"{base_url}/iserver/marketdata/snapshot", params={
                        "conids": str(conid),
                        "fields": "31,84,86"  # Last, Bid, Ask
                    })
                    
                    if resp2.ok:
                        market_data = resp2.json()
                        if market_data and len(market_data) > 0:
                            data = market_data[0]
                            last = data.get('31', 'N/A')
                            bid = data.get('84', 'N/A')
                            ask = data.get('86', 'N/A')
                            print(f"   📊 Market Data: Last=${last}, Bid=${bid}, Ask=${ask}")
                        else:
                            print("   ⚠️  No market data available")
                    else:
                        print(f"   ⚠️  Market data failed: {resp2.status_code}")
                else:
                    print("   ❌ Contract not found")
            else:
                print(f"   ❌ Search failed: {resp.status_code}")
    
    # Provide recommendations
    print("\n" + "=" * 50)
    print("📝 RECOMMENDATIONS FOR TESTING:")
    print("=" * 50)
    
    if expirations and strikes:
        nearest_expiry = expirations[0]
        middle_strike = strikes[len(strikes)//2]
        
        print(f"✅ Use these values for testing orders:")
        print(f"   Symbol: SPY")
        print(f"   Expiry: {nearest_expiry}")
        print(f"   Strike: {middle_strike}")
        print(f"   Type: C (for Call) or P (for Put)")
        print()
        print("🎯 Example order inputs:")
        print(f"   SPY {nearest_expiry} {middle_strike} C")
        print(f"   SPY {nearest_expiry} {middle_strike} P")
    else:
        print("❌ No valid options found - may need IB Gateway for full functionality")

if __name__ == "__main__":
    try:
        find_valid_spy_options()
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nMake sure:")
        print("1. Client Portal Gateway is running")
        print("2. You're authenticated at https://localhost:5000")
        print("3. Try running: python main.py and select option 3 first")
