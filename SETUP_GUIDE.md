# Complete Setup Guide for Interactive Brokers Options Trading

## 🚀 Quick Start (Windows)

### Step 1: Run Automated Setup
```bash
python setup_windows.py
```

This will:
- ✅ Check Python version
- ✅ Install dependencies
- ✅ Download Client Portal Gateway
- ✅ Create startup scripts
- ✅ Check Java installation

### Step 2: Manual Downloads

1. **Download IB Gateway**
   - Go to: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php
   - Download Windows 64-bit version
   - Install with default settings

### Step 3: Setup Paper Trading Account

1. **Login to Client Portal**
   - Go to: https://portal.interactivebrokers.com
   - Login with your Interactive Brokers account

2. **Configure Paper Trading**
   - Click Settings (gear icon)
   - Go to "Account Configuration" → "Paper Trading Account"
   - Note your **Paper Trading Username**
   - Click "Reset Paper Trading Password" to set a new password
   - **IMPORTANT**: Save these credentials separately!

### Step 4: Start IB Gateway

1. **Launch IB Gateway**
   - Double-click IB Gateway icon on desktop
   - **Login with your LIVE account credentials** (not paper trading)

2. **Configure API Settings**
   - Go to Configure → API → Settings
   - ✅ Enable ActiveX and Socket Clients
   - ✅ Socket Port: **7497** (for paper trading)
   - ✅ Master API Client ID: 0
   - ✅ Read-Only API: Checked
   - ✅ Trusted IPs: Add **127.0.0.1**
   - Click OK and Apply

### Step 5: Start Client Portal Gateway

1. **Run Gateway Script**
   ```bash
   # Option 1: Windows Batch File
   start_gateway.bat
   
   # Option 2: Python Script
   python start_gateway.py
   ```

2. **Wait for Startup**
   - You should see: "Starting Interactive Brokers Client Portal Gateway..."
   - Wait for the gateway to fully load

### Step 6: Authenticate Client Portal Gateway

1. **Open Browser**
   - Navigate to: https://localhost:5000
   - **Accept the SSL certificate warning** (it's self-signed)

2. **Login with Paper Trading Credentials**
   - Username: Your **Paper Trading Username** (from Step 3)
   - Password: Your **Paper Trading Password** (from Step 3)
   - **NOT your regular IB account credentials!**

3. **Confirm Success**
   - You should see "Client login succeeds"
   - **Keep this browser tab open**

### Step 7: Test Connection

```bash
python test_connection.py
```

Expected output:
```
✅ Connection successful!
✅ Already authenticated
✅ Paper trading account detected: DU123456
```

### Step 8: Run the Application

```bash
python main.py
```

## 🔧 Troubleshooting

### Problem: "Connection refused"

**Cause**: Client Portal Gateway not running

**Solution**:
1. Make sure you ran `start_gateway.bat`
2. Wait 30-60 seconds for gateway to start
3. Check if Java is installed: `java -version`

### Problem: "Not authenticated" (401 error)

**Cause**: Not logged into Client Portal Gateway

**Solution**:
1. Go to https://localhost:5000
2. Login with **Paper Trading** credentials (not live account)
3. Wait for "Client login succeeds" message
4. Keep browser tab open

### Problem: "SSL Certificate Error"

**Cause**: Browser blocking self-signed certificate

**Solution**:
1. In browser, click "Advanced"
2. Click "Proceed to localhost (unsafe)"
3. This is normal for IB's self-signed certificate

### Problem: "Paper trading account not detected"

**Cause**: Logged in with wrong credentials

**Solution**:
1. Logout from https://localhost:5000
2. Login with Paper Trading credentials (username starts with DU)
3. NOT your regular IB account credentials

### Problem: "Java not found"

**Cause**: Java not installed or not in PATH

**Solution**:
1. Download Java from: https://www.oracle.com/java/technologies/downloads/
2. Install Java 8 or higher
3. Restart command prompt
4. Test: `java -version`

### Problem: Gateway starts but immediately closes

**Cause**: Port conflict or Java issues

**Solution**:
1. Check if port 5000 is in use: `netstat -an | findstr :5000`
2. Close other applications using port 5000
3. Try running gateway as administrator

## 📋 Verification Checklist

Before running the application, verify:

- [ ] IB Gateway is running and logged in with LIVE credentials
- [ ] API is enabled in IB Gateway (port 7497)
- [ ] Client Portal Gateway is running (`start_gateway.bat`)
- [ ] Authenticated at https://localhost:5000 with PAPER TRADING credentials
- [ ] Browser shows "Client login succeeds"
- [ ] `python test_connection.py` shows all green checkmarks
- [ ] Paper trading account detected (DU prefix)

## 🔑 Important Notes

### Account Types
- **IB Gateway**: Login with your **LIVE** account credentials
- **Client Portal Gateway**: Login with your **PAPER TRADING** credentials
- These are different usernames and passwords!

### Ports
- **IB Gateway**: Uses port 7497 for paper trading API
- **Client Portal Gateway**: Uses port 5000 for web API

### Security
- Client Portal Gateway uses self-signed SSL certificates
- This is normal and expected
- The connection is still encrypted

### Session Management
- Keep the browser tab open at https://localhost:5000
- Session expires after 24 hours of inactivity
- Use the `/tickle` endpoint to keep session alive

## 📞 Getting Help

If you're still having issues:

1. **Check IB Documentation**
   - https://www.interactivebrokers.com/campus/ibkr-api-page/cpapi-v1/

2. **Create Support Ticket**
   - https://www.interactivebrokers.com/sso/resolver?action=NEW_TICKET
   - Select "API" category

3. **Common Issues Forum**
   - Search for similar issues in IB forums
   - Many setup problems have been solved before

## 🎯 Quick Commands Reference

```bash
# Setup (run once)
python setup_windows.py

# Start gateway
start_gateway.bat

# Test connection
python test_connection.py

# Run application
python main.py

# Check Java
java -version

# Check port usage
netstat -an | findstr :5000
netstat -an | findstr :7497
```

## 🔄 Daily Workflow

1. Start IB Gateway (login with live credentials)
2. Start Client Portal Gateway (`start_gateway.bat`)
3. Authenticate at https://localhost:5000 (paper trading credentials)
4. Run your trading application (`python main.py`)
5. Keep browser tab open during trading session
