"""
Test IB API using subprocess calls to avoid Python environment issues
"""

import subprocess
import json
import sys

class IBTestWithCurl:
    def __init__(self):
        self.base_url = "https://localhost:5000/v1/api"
        self.account_id = None
        
    def curl_request(self, endpoint, method="GET", data=None):
        """Make HTTP request using curl"""
        url = f"{self.base_url}{endpoint}"
        
        if sys.platform.startswith('win'):
            # Windows PowerShell
            if method == "GET":
                cmd = f'powershell -Command "try {{ $response = Invoke-WebRequest -Uri \'{url}\' -SkipCertificateCheck -UseBasicParsing; $response.Content }} catch {{ $_.Exception.Message }}"'
            else:
                # POST request
                json_data = json.dumps(data) if data else "{}"
                cmd = f'powershell -Command "try {{ $response = Invoke-WebRequest -Uri \'{url}\' -Method POST -Body \'{json_data}\' -ContentType \'application/json\' -SkipCertificateCheck -UseBasicParsing; $response.Content }} catch {{ $_.Exception.Message }}"'
        else:
            # Unix/Linux
            if method == "GET":
                cmd = f"curl -k -s '{url}'"
            else:
                json_data = json.dumps(data) if data else "{}"
                cmd = f"curl -k -s -X POST -H 'Content-Type: application/json' -d '{json_data}' '{url}'"
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.stdout.strip()
        except subprocess.TimeoutExpired:
            return "TIMEOUT"
        except Exception as e:
            return f"ERROR: {e}"
    
    def test_authentication(self):
        """Test authentication"""
        print("🔐 Testing Authentication...")
        response = self.curl_request("/iserver/auth/status")
        
        try:
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Authentication failed: {response}")
                return False
            
            # Try to parse JSON
            auth_data = json.loads(response)
            if auth_data.get('authenticated'):
                server = auth_data.get('serverInfo', {}).get('serverName', 'Unknown')
                print(f"✅ Authentication successful - Server: {server}")
                return True
            else:
                print("❌ Not authenticated - please login at https://localhost:5000")
                return False
        except json.JSONDecodeError:
            if "authenticated" in response.lower():
                print("✅ Authentication appears successful (partial response)")
                return True
            print(f"❌ Invalid response: {response[:100]}...")
            return False
    
    def test_accounts(self):
        """Test account access"""
        print("\n💰 Testing Account Access...")
        response = self.curl_request("/portfolio/accounts")
        
        try:
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Account access failed: {response}")
                return False
            
            accounts = json.loads(response)
            if accounts and len(accounts) > 0:
                self.account_id = accounts[0]['id']
                print(f"✅ Account found: {self.account_id}")
                return True
            else:
                print("❌ No accounts found")
                return False
        except json.JSONDecodeError:
            print(f"❌ Invalid account response: {response[:100]}...")
            return False
    
    def test_account_summary(self):
        """Test account summary"""
        if not self.account_id:
            print("❌ No account ID available")
            return False
        
        print("\n📊 Testing Account Summary...")
        response = self.curl_request(f"/iserver/account/{self.account_id}/summary")
        
        try:
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Account summary failed: {response}")
                return False
            
            summary = json.loads(response)
            balance = summary.get('balance', 0)
            buying_power = summary.get('buyingPower', 0)
            net_liq = summary.get('netLiquidationValue', 0)
            
            print(f"✅ Account Summary Retrieved:")
            print(f"   💵 Cash Balance: ${balance:,.2f}")
            print(f"   💪 Buying Power: ${buying_power:,.2f}")
            print(f"   📈 Net Liquidation: ${net_liq:,.2f}")
            
            return True
        except json.JSONDecodeError:
            print(f"❌ Invalid summary response: {response[:100]}...")
            return False
    
    def test_symbol_search(self):
        """Test symbol search"""
        print("\n🔍 Testing Symbol Search...")
        response = self.curl_request("/trsrv/secdef/search?symbol=SPY&secType=STK")
        
        try:
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Symbol search failed: {response}")
                return False
            
            results = json.loads(response)
            if results and len(results) > 0:
                conid = results[0]['conid']
                symbol = results[0].get('symbol', 'SPY')
                print(f"✅ Symbol search successful: {symbol} (ConID: {conid})")
                return True
            else:
                print("❌ No symbol results found")
                return False
        except json.JSONDecodeError:
            print(f"❌ Invalid search response: {response[:100]}...")
            return False
    
    def test_market_data(self):
        """Test market data"""
        print("\n📈 Testing Market Data...")
        
        # First get SPY ConID
        search_response = self.curl_request("/trsrv/secdef/search?symbol=SPY&secType=STK")
        
        try:
            search_results = json.loads(search_response)
            if not search_results:
                print("❌ Could not find SPY for market data test")
                return False
            
            conid = search_results[0]['conid']
            
            # Get market data
            response = self.curl_request(f"/iserver/marketdata/snapshot?conids={conid}&fields=31,84,86,87")
            
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Market data failed: {response}")
                return False
            
            market_data = json.loads(response)
            if market_data and len(market_data) > 0:
                data = market_data[0]
                last = data.get('31', 'N/A')
                bid = data.get('84', 'N/A')
                ask = data.get('86', 'N/A')
                volume = data.get('87', 'N/A')
                
                print(f"✅ Market Data Retrieved for SPY:")
                print(f"   💲 Last: ${last}")
                print(f"   📉 Bid: ${bid}")
                print(f"   📈 Ask: ${ask}")
                print(f"   📊 Volume: {volume}")
                return True
            else:
                print("❌ No market data returned")
                return False
        except json.JSONDecodeError:
            print(f"❌ Invalid market data response: {response[:100]}...")
            return False
    
    def test_order_validation(self):
        """Test order validation"""
        print("\n📝 Testing Order Validation...")
        
        # Get SPY ConID first
        search_response = self.curl_request("/trsrv/secdef/search?symbol=SPY&secType=STK")
        
        try:
            search_results = json.loads(search_response)
            if not search_results:
                print("❌ Could not find SPY for order validation")
                return False
            
            conid = search_results[0]['conid']
            
            # Create test order
            order_data = {
                "conid": str(conid),
                "orderType": "MKT",
                "side": "BUY",
                "quantity": 1,
                "tif": "DAY"
            }
            
            response = self.curl_request("/iserver/account/orders/whatif", "POST", order_data)
            
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Order validation failed: {response}")
                return False
            
            # Check if response contains validation info
            if "commission" in response.lower() or "equity" in response.lower() or "amount" in response.lower():
                print("✅ Order validation successful")
                return True
            else:
                print(f"❌ Unexpected validation response: {response[:100]}...")
                return False
        except json.JSONDecodeError:
            print(f"❌ Invalid validation response: {response[:100]}...")
            return False
    
    def test_orders_status(self):
        """Test order status"""
        print("\n📋 Testing Order Status...")
        response = self.curl_request("/iserver/account/orders")
        
        try:
            if response.startswith("ERROR") or response == "TIMEOUT":
                print(f"❌ Order status failed: {response}")
                return False
            
            orders = json.loads(response)
            print(f"✅ Order status retrieved: {len(orders)} orders found")
            
            # Count options orders
            options_orders = 0
            for order in orders:
                if isinstance(order, dict) and order.get('assetClass') == 'OPT':
                    options_orders += 1
            
            print(f"   📊 Options orders: {options_orders}")
            return True
        except json.JSONDecodeError:
            print(f"❌ Invalid orders response: {response[:100]}...")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Interactive Brokers Options Trading - Comprehensive Test Suite")
        print("=" * 80)
        
        tests = [
            ("Authentication", self.test_authentication),
            ("Account Access", self.test_accounts),
            ("Account Summary", self.test_account_summary),
            ("Symbol Search", self.test_symbol_search),
            ("Market Data", self.test_market_data),
            ("Order Validation", self.test_order_validation),
            ("Order Status", self.test_orders_status)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
        
        # Summary
        print("\n" + "=" * 80)
        print("🏁 TEST RESULTS SUMMARY")
        print("=" * 80)
        print(f"✅ Tests Passed: {passed}/{total}")
        print(f"📊 Success Rate: {(passed/total)*100:.1f}%")
        
        if passed >= 4:
            print("\n🎉 EXCELLENT! Your IB Options Trading setup is working well!")
            print("✅ Core functionality is operational")
            print("✅ Ready for options trading")
        elif passed >= 2:
            print("\n⚠️  PARTIAL SUCCESS - Basic functionality working")
            print("✅ Authentication and account access working")
            print("⚠️  Some advanced features may need IB Gateway")
        else:
            print("\n❌ SETUP ISSUES DETECTED")
            print("❌ Please check your gateway and authentication")
        
        return passed >= 2

if __name__ == "__main__":
    tester = IBTestWithCurl()
    tester.run_all_tests()
