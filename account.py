"""
Account management module for Interactive Brokers Options Trading
"""

import logging
from config import BASE_URL

logger = logging.getLogger(__name__)

class AccountManager:
    def __init__(self, session):
        self.session = session
    
    def get_accounts(self):
        """Get list of available accounts"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/account")
            if resp.ok:
                accounts = resp.json()
                logger.info(f"Found {len(accounts)} accounts")
                return accounts
            else:
                logger.error(f"Failed to get accounts: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting accounts: {e}")
            return None
    
    def get_account_balances(self, account_id=None):
        """Fetch current account balances relevant to Options trading"""
        try:
            if not account_id:
                accounts = self.get_accounts()
                if not accounts:
                    return None
                account_id = accounts[0]['id']
            
            resp = self.session.get(f"{BASE_URL}/iserver/account/{account_id}/summary")
            if resp.ok:
                balances = resp.json()
                
                # Filter relevant balance information for options trading
                relevant_keys = [
                    'TotalCashValue', 'AvailableFunds', 'BuyingPower', 
                    'OptionMarketValue', 'NetLiquidation', 'ExcessLiquidity',
                    'InitMarginReq', 'MaintMarginReq'
                ]
                
                filtered_balances = {}
                for item in balances:
                    key = item.get('key')
                    if key in relevant_keys:
                        filtered_balances[key] = {
                            'value': item.get('value'),
                            'currency': item.get('currency', 'USD')
                        }
                
                logger.info(f"Retrieved account balances for account {account_id}")
                return filtered_balances
            else:
                logger.error(f"Failed to get account balances: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting account balances: {e}")
            return None
    
    def display_account_balances(self, balances):
        """Display account balances in a formatted way"""
        if not balances:
            print("No balance information available")
            return
        
        print("\n===== Account Balances for Options Trading =====")
        for key, data in balances.items():
            value = data['value']
            currency = data['currency']
            print(f"{key:20}: {value:>15} {currency}")
        print("=" * 50)
    
    def get_account_summary(self, account_id=None):
        """Get comprehensive account summary"""
        try:
            if not account_id:
                accounts = self.get_accounts()
                if not accounts:
                    return None
                account_id = accounts[0]['id']
            
            resp = self.session.get(f"{BASE_URL}/iserver/account/{account_id}/summary")
            if resp.ok:
                return resp.json()
            else:
                logger.error(f"Failed to get account summary: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting account summary: {e}")
            return None
