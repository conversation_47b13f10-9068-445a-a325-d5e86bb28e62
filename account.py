"""
Account management module for Interactive Brokers Options Trading
"""

import logging
from config import BASE_URL

logger = logging.getLogger(__name__)

class AccountManager:
    def __init__(self, session):
        self.session = session
    
    def get_accounts(self):
        """Get list of available accounts"""
        try:
            # Try portfolio accounts first (more detailed)
            resp = self.session.get(f"{BASE_URL}/portfolio/accounts")
            if resp.ok:
                accounts = resp.json()
                logger.info(f"Found {len(accounts)} accounts")
                return accounts

            # Fallback to iserver accounts
            resp = self.session.get(f"{BASE_URL}/iserver/accounts")
            if resp.ok:
                accounts_data = resp.json()
                accounts = accounts_data.get('accounts', [])
                logger.info(f"Found {len(accounts)} accounts (fallback)")
                return [{'id': acc} for acc in accounts]
            else:
                logger.error(f"Failed to get accounts: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting accounts: {e}")
            return None
    
    def get_account_balances(self, account_id=None):
        """Fetch current account balances relevant to Options trading"""
        try:
            if not account_id:
                accounts = self.get_accounts()
                if not accounts:
                    return None
                # Handle both string and dict account formats
                if isinstance(accounts[0], dict):
                    account_id = accounts[0]['id']
                else:
                    account_id = accounts[0]
            
            resp = self.session.get(f"{BASE_URL}/iserver/account/{account_id}/summary")
            if resp.ok:
                balances = resp.json()

                # Map the direct response format to our expected format
                relevant_mapping = {
                    'totalCashValue': 'balance',
                    'availableFunds': 'availableFunds',
                    'buyingPower': 'buyingPower',
                    'netLiquidationValue': 'netLiquidationValue',
                    'excessLiquidity': 'excessLiquidity',
                    'equityWithLoanValue': 'equityWithLoanValue'
                }

                filtered_balances = {}
                for our_key, api_key in relevant_mapping.items():
                    if api_key in balances:
                        filtered_balances[our_key] = {
                            'value': balances[api_key],
                            'currency': 'USD'  # Default currency
                        }

                logger.info(f"Retrieved account balances for account {account_id}")
                return filtered_balances
            else:
                logger.error(f"Failed to get account balances: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting account balances: {e}")
            return None
    
    def display_account_balances(self, balances):
        """Display account balances in a formatted way"""
        if not balances:
            print("No balance information available")
            return
        
        print("\n===== Account Balances for Options Trading =====")
        for key, data in balances.items():
            value = data['value']
            currency = data['currency']
            print(f"{key:20}: {value:>15} {currency}")
        print("=" * 50)
    
    def get_account_summary(self, account_id=None):
        """Get comprehensive account summary"""
        try:
            if not account_id:
                accounts = self.get_accounts()
                if not accounts:
                    return None
                account_id = accounts[0]['id']
            
            resp = self.session.get(f"{BASE_URL}/iserver/account/{account_id}/summary")
            if resp.ok:
                return resp.json()
            else:
                logger.error(f"Failed to get account summary: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting account summary: {e}")
            return None
