@echo off
title Interactive Brokers Options Trading - Quick Start
color 0A

echo.
echo ================================================================
echo  Interactive Brokers Options Trading - Quick Start
echo ================================================================
echo.

echo [1/4] Running setup script...
python setup_windows.py
if errorlevel 1 (
    echo.
    echo ❌ Setup failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo [2/4] Testing connection...
python test_connection.py
if errorlevel 1 (
    echo.
    echo ⚠️  Connection test failed. This is normal if gateways aren't running yet.
    echo.
    echo Please follow these steps:
    echo 1. Download and install IB Gateway from the link shown above
    echo 2. Run start_gateway.bat to start Client Portal Gateway
    echo 3. Login at https://localhost:5000 with paper trading credentials
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo.
echo [3/4] All systems ready!
echo.
echo [4/4] Starting the trading application...
echo.
python main.py

echo.
echo Application closed. Press any key to exit...
pause > nul
