@echo off
title IB Client Portal Gateway - Auto Restart
color 0A

echo ================================================================
echo  Interactive Brokers Client Portal Gateway - Auto Restart
echo ================================================================
echo.
echo This script will:
echo 1. Start the Client Portal Gateway
echo 2. Automatically restart it if it crashes
echo 3. Keep running until you close this window
echo.
echo IMPORTANT STEPS:
echo 1. After gateway starts, go to: https://localhost:5000
echo 2. Login with your PAPER TRADING credentials
echo 3. Keep this window open while using the application
echo 4. Press Ctrl+C to stop the gateway
echo.
echo ================================================================
echo.

:RESTART_LOOP
echo [%TIME%] Starting Client Portal Gateway...
echo.

.\bin\run.bat root\conf.yaml

echo.
echo [%TIME%] Gateway stopped. Checking if intentional...
echo.

choice /C YN /T 10 /D Y /M "Restart gateway automatically? (Y/N, auto-restart in 10 seconds)"

if errorlevel 2 goto END
if errorlevel 1 goto RESTART_LOOP

:END
echo.
echo [%TIME%] Gateway shutdown complete.
echo Press any key to close this window...
pause > nul
