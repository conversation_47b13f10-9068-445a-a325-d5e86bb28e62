@echo off
title Start IB Gateway for Options Trading
color 0A

echo ================================================================
echo  Starting IB Gateway for Options Trading
echo ================================================================
echo.
echo This script will help you start IB Gateway for full options
echo trading functionality.
echo.
echo STEPS:
echo 1. This will open IB Gateway
echo 2. Login with your LIVE account credentials
echo 3. Configure API settings for paper trading
echo 4. Keep both gateways running
echo.
echo ================================================================
echo.

echo Looking for IB Gateway installation...

if exist "C:\Jts\ibgateway.exe" (
    echo ✅ Found IB Gateway at C:\Jts\ibgateway.exe
    echo Starting IB Gateway...
    start "" "C:\Jts\ibgateway.exe"
    goto CONFIGURE
)

if exist "C:\Jts\tws.exe" (
    echo ✅ Found TWS at C:\Jts\tws.exe
    echo Starting TWS...
    start "" "C:\Jts\tws.exe"
    goto CONFIGURE
)

if exist "C:\Program Files\Interactive Brokers\TWS API\IBGateway.exe" (
    echo ✅ Found IB Gateway in Program Files
    echo Starting IB Gateway...
    start "" "C:\Program Files\Interactive Brokers\TWS API\IBGateway.exe"
    goto CONFIGURE
)

echo ❌ IB Gateway not found in common locations
echo.
echo Please manually start IB Gateway:
echo 1. Look for "IB Gateway" in Start Menu
echo 2. Or go to your IB installation folder
echo 3. Double-click ibgateway.exe or tws.exe
echo.
goto CONFIGURE

:CONFIGURE
echo.
echo ================================================================
echo  CONFIGURATION INSTRUCTIONS
echo ================================================================
echo.
echo After IB Gateway opens:
echo.
echo 1. LOGIN:
echo    → Use your LIVE account credentials
echo    → NOT your paper trading credentials
echo.
echo 2. CONFIGURE API:
echo    → Go to Configure → API → Settings
echo    → ✅ Enable ActiveX and Socket Clients
echo    → ✅ Socket Port: 7497 (paper trading)
echo    → ✅ Master API Client ID: 0
echo    → ✅ Read-Only API: Checked
echo    → ✅ Trusted IPs: Add 127.0.0.1
echo    → Click OK and Apply
echo.
echo 3. VERIFY:
echo    → You should see "API" status in the gateway
echo    → Port 7497 should be listening
echo.
echo 4. TEST:
echo    → Run: python diagnose_endpoints.py
echo    → Should show port 7497 as active
echo    → Run: python reference_implementation.py
echo.
echo ================================================================
echo.
echo Press any key when you've completed the configuration...
pause > nul

echo.
echo Testing if IB Gateway is properly configured...
echo.

python diagnose_endpoints.py

echo.
echo ================================================================
echo If the diagnostic shows port 7497 as active, you're ready!
echo Run: python reference_implementation.py
echo ================================================================
echo.
pause
