"""
Simple test to check if Client Portal Gateway is running
"""

import requests
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

def test_gateway():
    """Test if gateway is running"""
    try:
        session = requests.Session()
        session.verify = False
        
        print("Testing connection to Client Portal Gateway...")
        resp = session.get("https://localhost:5000/v1/api/iserver/auth/status", timeout=10)
        
        if resp.ok:
            data = resp.json()
            print("SUCCESS: Gateway is running!")
            print(f"Response: {data}")
            
            if data.get('authenticated', False):
                print("SUCCESS: Already authenticated!")
            else:
                print("INFO: Not authenticated yet")
                print("Next step: Go to https://localhost:5000 and login with paper trading credentials")
            
            return True
        else:
            print(f"ERROR: Gateway responded with status {resp.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("ERROR: Cannot connect to gateway")
        print("Make sure the gateway is running with: .\\bin\\run.bat root\\conf.yaml")
        return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    test_gateway()
