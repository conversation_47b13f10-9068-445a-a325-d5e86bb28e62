"""
Position management module for Interactive Brokers Options Trading
"""

import logging
from config import BASE_URL

logger = logging.getLogger(__name__)

class PositionManager:
    def __init__(self, session):
        self.session = session
    
    def get_all_positions(self, account_id="0"):
        """Get all positions for the account"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/account/positions/{account_id}")
            if resp.ok:
                positions = resp.json()
                logger.info(f"Retrieved {len(positions)} total positions")
                return positions
            else:
                logger.error(f"Failed to get positions: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return None
    
    def get_options_positions(self, account_id="0"):
        """Fetch current open Options positions"""
        try:
            positions = self.get_all_positions(account_id)
            if not positions:
                return []
            
            options_positions = []
            for position in positions:
                if position.get('assetClass') == 'OPT':
                    options_positions.append({
                        'conid': position.get('conid'),
                        'symbol': position.get('ticker'),
                        'position': position.get('position'),
                        'market_price': position.get('mktPrice'),
                        'market_value': position.get('mktValue'),
                        'unrealized_pnl': position.get('unrealizedPnl'),
                        'realized_pnl': position.get('realizedPnl'),
                        'expiry': position.get('expiry'),
                        'strike': position.get('strike'),
                        'right': position.get('right'),  # 'C' for Call, 'P' for Put
                        'multiplier': position.get('multiplier', 100),
                        'currency': position.get('currency', 'USD'),
                        'avg_cost': position.get('avgCost'),
                        'avg_price': position.get('avgPrice')
                    })
            
            logger.info(f"Found {len(options_positions)} options positions")
            return options_positions
        except Exception as e:
            logger.error(f"Error getting options positions: {e}")
            return []
    
    def display_options_positions(self, positions):
        """Display options positions in a formatted way"""
        if not positions:
            print("No open options positions found")
            return
        
        print("\n===== Current Options Positions =====")
        print(f"{'Symbol':<8} {'Expiry':<10} {'Strike':<8} {'Type':<4} {'Qty':<6} {'Price':<10} {'P&L':<12}")
        print("-" * 70)
        
        for pos in positions:
            option_type = "Call" if pos['right'] == 'C' else "Put"
            symbol = pos['symbol'] or 'N/A'
            expiry = pos['expiry'] or 'N/A'
            strike = f"${pos['strike']}" if pos['strike'] else 'N/A'
            quantity = pos['position'] or 0
            price = f"${pos['market_price']:.2f}" if pos['market_price'] else 'N/A'
            pnl = f"${pos['unrealized_pnl']:.2f}" if pos['unrealized_pnl'] else 'N/A'
            
            print(f"{symbol:<8} {expiry:<10} {strike:<8} {option_type:<4} {quantity:<6} {price:<10} {pnl:<12}")
        
        print("=" * 70)
    
    def get_position_by_conid(self, conid, account_id="0"):
        """Get specific position by contract ID"""
        try:
            positions = self.get_all_positions(account_id)
            if not positions:
                return None
            
            for position in positions:
                if str(position.get('conid')) == str(conid):
                    return position
            
            logger.warning(f"Position with conid {conid} not found")
            return None
        except Exception as e:
            logger.error(f"Error getting position by conid: {e}")
            return None
    
    def get_positions_summary(self, account_id="0"):
        """Get summary of all positions"""
        try:
            positions = self.get_all_positions(account_id)
            if not positions:
                return None
            
            summary = {
                'total_positions': len(positions),
                'options_positions': 0,
                'stock_positions': 0,
                'total_market_value': 0,
                'total_unrealized_pnl': 0
            }
            
            for position in positions:
                asset_class = position.get('assetClass', '')
                if asset_class == 'OPT':
                    summary['options_positions'] += 1
                elif asset_class == 'STK':
                    summary['stock_positions'] += 1
                
                market_value = position.get('mktValue', 0) or 0
                unrealized_pnl = position.get('unrealizedPnl', 0) or 0
                
                summary['total_market_value'] += market_value
                summary['total_unrealized_pnl'] += unrealized_pnl
            
            return summary
        except Exception as e:
            logger.error(f"Error getting positions summary: {e}")
            return None
