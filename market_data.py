"""
Market data module for Interactive Brokers Options Trading
"""

import logging
from config import BASE_URL, OPTION_MARKET_DATA_FIELDS

logger = logging.getLogger(__name__)

class MarketDataManager:
    def __init__(self, session):
        self.session = session
    
    def get_option_market_data(self, conid):
        """Get real-time market data for a specific option contract by ConID"""
        try:
            resp = self.session.get(f"{BASE_URL}/iserver/marketdata/snapshot", params={
                "conids": str(conid),
                "fields": OPTION_MARKET_DATA_FIELDS
            })
            
            if resp.ok:
                market_data = resp.json()
                if market_data and len(market_data) > 0:
                    data = market_data[0]
                    
                    # Parse market data fields
                    parsed_data = {
                        'conid': conid,
                        'last_price': data.get('31'),
                        'bid': data.get('84'),
                        'ask': data.get('86'),
                        'volume': data.get('87'),
                        'delta': data.get('7308'),
                        'gamma': data.get('7309'),
                        'theta': data.get('7295'),
                        'vega': data.get('7296'),
                        'implied_volatility': data.get('7633'),
                        'bid_size': data.get('88'),
                        'ask_size': data.get('85')
                    }
                    
                    logger.info(f"Retrieved market data for ConID {conid}")
                    return parsed_data
                else:
                    logger.warning(f"No market data returned for ConID {conid}")
                    return None
            else:
                logger.error(f"Failed to get market data: {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error getting option market data: {e}")
            return None
    
    def get_option_market_data_by_contract(self, underlying_symbol, expiry, strike, right, options_search):
        """Get market data for option contract by contract details"""
        try:
            option_info = options_search.search_specific_option(underlying_symbol, expiry, strike, right)
            if not option_info:
                return None
            
            return self.get_option_market_data(option_info['conid'])
        except Exception as e:
            logger.error(f"Error getting market data by contract: {e}")
            return None
    
    def display_option_market_data(self, market_data, contract_info=None):
        """Display option market data in a formatted way"""
        if not market_data:
            print("No market data available")
            return
        
        print("\n===== Option Market Data =====")
        
        if contract_info:
            symbol = contract_info.get('symbol', 'N/A')
            expiry = contract_info.get('expiry', 'N/A')
            strike = contract_info.get('strike', 'N/A')
            right = contract_info.get('right', 'N/A')
            option_type = "Call" if right == 'C' else "Put" if right == 'P' else 'N/A'
            
            print(f"Contract: {symbol} {expiry} ${strike} {option_type}")
            print("-" * 40)
        
        # Price Information
        last_price = market_data.get('last_price')
        bid = market_data.get('bid')
        ask = market_data.get('ask')
        volume = market_data.get('volume')
        
        print("Price Information:")
        print(f"  Last Price: ${last_price:.2f}" if last_price else "  Last Price: N/A")
        print(f"  Bid:        ${bid:.2f}" if bid else "  Bid:        N/A")
        print(f"  Ask:        ${ask:.2f}" if ask else "  Ask:        N/A")
        print(f"  Volume:     {volume:,}" if volume else "  Volume:     N/A")
        
        # Greeks Information
        delta = market_data.get('delta')
        gamma = market_data.get('gamma')
        theta = market_data.get('theta')
        vega = market_data.get('vega')
        iv = market_data.get('implied_volatility')
        
        print("\nGreeks:")
        print(f"  Delta:      {delta:.4f}" if delta else "  Delta:      N/A")
        print(f"  Gamma:      {gamma:.4f}" if gamma else "  Gamma:      N/A")
        print(f"  Theta:      {theta:.4f}" if theta else "  Theta:      N/A")
        print(f"  Vega:       {vega:.4f}" if vega else "  Vega:       N/A")
        print(f"  Impl. Vol:  {iv:.2%}" if iv else "  Impl. Vol:  N/A")
        
        print("=" * 40)
    
    def get_multiple_option_quotes(self, conids):
        """Get market data for multiple option contracts"""
        try:
            if not conids:
                return []
            
            conids_str = ",".join(str(conid) for conid in conids)
            resp = self.session.get(f"{BASE_URL}/iserver/marketdata/snapshot", params={
                "conids": conids_str,
                "fields": OPTION_MARKET_DATA_FIELDS
            })
            
            if resp.ok:
                market_data_list = resp.json()
                parsed_data_list = []
                
                for data in market_data_list:
                    parsed_data = {
                        'conid': data.get('conid'),
                        'last_price': data.get('31'),
                        'bid': data.get('84'),
                        'ask': data.get('86'),
                        'volume': data.get('87'),
                        'delta': data.get('7308'),
                        'gamma': data.get('7309'),
                        'theta': data.get('7295'),
                        'vega': data.get('7296')
                    }
                    parsed_data_list.append(parsed_data)
                
                logger.info(f"Retrieved market data for {len(parsed_data_list)} contracts")
                return parsed_data_list
            else:
                logger.error(f"Failed to get multiple quotes: {resp.status_code}")
                return []
        except Exception as e:
            logger.error(f"Error getting multiple option quotes: {e}")
            return []
    
    def get_underlying_price(self, symbol):
        """Get current price of underlying stock"""
        try:
            # First get the ConID for the underlying
            resp = self.session.get(f"{BASE_URL}/trsrv/secdef/search", params={
                "symbol": symbol,
                "secType": "STK"
            })
            
            if not resp.ok:
                logger.error(f"Failed to search for underlying: {resp.status_code}")
                return None
            
            results = resp.json()
            if not results:
                logger.warning(f"No results found for underlying symbol {symbol}")
                return None
            
            conid = results[0]['conid']
            
            # Get market data for the underlying
            resp = self.session.get(f"{BASE_URL}/iserver/marketdata/snapshot", params={
                "conids": str(conid),
                "fields": "31,84,86,87"  # Last, Bid, Ask, Volume
            })
            
            if resp.ok:
                market_data = resp.json()
                if market_data and len(market_data) > 0:
                    data = market_data[0]
                    return {
                        'symbol': symbol,
                        'last_price': data.get('31'),
                        'bid': data.get('84'),
                        'ask': data.get('86'),
                        'volume': data.get('87')
                    }
            
            logger.error(f"Failed to get underlying price: {resp.status_code}")
            return None
        except Exception as e:
            logger.error(f"Error getting underlying price: {e}")
            return None
