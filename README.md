# Interactive Brokers Options Trading - Python POC

A modular Python application for Interactive Brokers Options trading using the Client Portal Web API.

## Project Structure

```
├── main.py              # Main application with CLI menu
├── auth.py              # Authentication management
├── account.py           # Account balance management
├── positions.py         # Position tracking
├── options_search.py    # Option contract search and chains
├── market_data.py       # Market data and Greeks
├── orders.py            # Order placement and management
├── config.py            # Configuration settings
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## Features

### ✅ Implemented Functionality

1. **Authentication Management**
   - Check authentication status
   - Session management
   - Reauthentication support

2. **Account Management**
   - Fetch account balances relevant to options trading
   - Display formatted balance information
   - Account summary retrieval

3. **Position Management**
   - Fetch current open options positions
   - Display positions with P&L information
   - Filter options positions from all positions

4. **Options Search & Chains**
   - Search for specific option contracts
   - Fetch option chains for underlying symbols
   - Get available strikes and expirations

5. **Market Data**
   - Real-time option market data
   - Greeks (Delta, Gamma, Theta, Vega)
   - Bid/Ask spreads and volume
   - Underlying stock prices

6. **Order Management**
   - Place market orders for options
   - Place limit orders for options
   - Order validation before placement
   - Order status tracking
   - Order cancellation
   - Support for single-leg options trading

7. **Interactive CLI Menu**
   - User-friendly command-line interface
   - Menu-driven navigation
   - Error handling and validation

## Prerequisites

### 1. Interactive Brokers Account Setup

1. **Create Paper Trading Account**
   - Sign up for Interactive Brokers account
   - Enable paper trading in Client Portal
   - Note your paper trading username and password

### 2. Download and Install IB Gateway

1. **Download IB Gateway for Windows**
   - Go to: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php
   - Download Windows version (64-bit recommended)
   - Run the installer and follow setup wizard

### 3. Download Client Portal Gateway

1. **Download Client Portal Gateway**
   - Go to: https://www.interactivebrokers.com/campus/ibkr-api-page/cpapi-v1/
   - Download the latest Client Portal Gateway ZIP file
   - Extract to a folder (e.g., `C:\clientportal.gw\`)

### 4. Setup Paper Trading Credentials

1. **Get Paper Trading Credentials**
   - Login to Client Portal: https://portal.interactivebrokers.com
   - Go to Settings → Account Configuration → Paper Trading Account
   - Note your Paper Trading Username and Account Number
   - Reset Paper Trading Password if needed

### 5. Start the Gateways

1. **Start IB Gateway**
   - Double-click IB Gateway icon on desktop
   - Login with your LIVE account credentials (not paper trading)
   - Configure API settings:
     - Enable API
     - Set Socket Port: 7497 (for paper trading)
     - Add Trusted IP: 127.0.0.1
     - Check "Read-Only API"

2. **Start Client Portal Gateway**
   ```bash
   # Navigate to Client Portal Gateway folder
   cd C:\clientportal.gw\

   # Windows - Run the batch file
   bin\run.bat

   # Or run directly
   java -cp "dist\*" ibgateway.GWClient
   ```

3. **Authenticate Client Portal Gateway**
   - Open browser: https://localhost:5000
   - Login with your PAPER TRADING credentials
   - Wait for "Client login succeeds" message
   - Keep this browser tab open

### 6. Python Environment

1. **Install Python Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Test Connection**
   ```bash
   python test_connection.py
   ```

## Usage

### Running the Application

```bash
python main.py
```

### Menu Options

```
===== Interactive Brokers Options Trading Menu =====
1. Fetch Account Balances
2. Fetch Open Option Positions
3. Fetch Option Chain for Symbol
4. Get Option Market Data
5. Place Option Order
6. Check Order Status
7. Cancel Order
8. Exit
=================================================
```

### Example Workflows

#### 1. Check Account Status
```
Choice: 1
```
Displays account balances including:
- Total Cash Value
- Available Funds
- Buying Power
- Option Market Value

#### 2. View Current Positions
```
Choice: 2
```
Shows all open options positions with:
- Symbol, expiry, strike, type
- Quantity and current price
- Unrealized P&L

#### 3. Research Options
```
Choice: 3
Enter underlying symbol: SPY
```
Displays available expirations and strikes for SPY options

#### 4. Get Market Data
```
Choice: 4
Enter underlying symbol: SPY
Enter expiry date: ********
Enter strike price: 580
Enter option type: C
```
Shows real-time market data and Greeks

#### 5. Place an Order
```
Choice: 5
Enter underlying symbol: SPY
Enter expiry date: ********
Enter strike price: 580
Enter option type: C
Enter side: BUY
Enter quantity: 1
Enter order type: LIMIT
Enter limit price: 5.50
```

## Configuration

Edit `config.py` to customize:

- **API Endpoints**: BASE_URL, ports
- **SSL Settings**: Certificate verification
- **Trading Parameters**: Time in force, order types
- **Market Data Fields**: Greeks and price fields
- **Logging**: Log levels and formats

## API Endpoints Used

| Endpoint | Purpose |
|----------|---------|
| `/iserver/auth/status` | Authentication status |
| `/iserver/account` | Account information |
| `/iserver/account/{id}/summary` | Account balances |
| `/iserver/account/positions/0` | Current positions |
| `/trsrv/secdef/search` | Option contract search |
| `/iserver/secdef/info` | Option chains |
| `/iserver/marketdata/snapshot` | Market data & Greeks |
| `/iserver/account/orders/whatif` | Order validation |
| `/iserver/account/orders` | Place/get orders |

## Error Handling

The application includes comprehensive error handling for:

- **Authentication Issues**: Automatic detection and guidance
- **Network Errors**: Connection timeouts and retries
- **Invalid Input**: User input validation
- **API Errors**: Detailed error messages from IB API
- **Order Rejections**: Clear feedback on order issues

## Logging

Logs are configured in `config.py`:
- **Level**: INFO (configurable)
- **Format**: Timestamp, module, level, message
- **Output**: Console (can be extended to files)

## Important Notes

1. **SSL Certificates**: IB uses self-signed certificates, so SSL verification is disabled
2. **Browser Authentication**: Initial login must be done via browser at https://localhost:5000
3. **Contract IDs**: All option orders require ConID for specific contracts
4. **Paper Trading**: This POC is designed for paper trading accounts
5. **Market Data**: Some Greeks may require market data subscriptions

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure IB Gateway is running
   - Check that Client Portal Gateway is started
   - Verify port 5000 is not blocked

2. **Authentication Failed**
   - Login via browser first at https://localhost:5000
   - Check paper trading account credentials
   - Verify API permissions are enabled

3. **Option Contract Not Found**
   - Verify expiry format (YYYYMMDD)
   - Check strike price exists
   - Ensure option type is 'C' or 'P'

4. **Order Rejected**
   - Check account has sufficient buying power
   - Verify option contract is actively traded
   - Ensure market is open for the security

## Next Steps

This Python POC provides the foundation for:

1. **C++ Implementation**: Port core functionality to C++ for performance
2. **Advanced Order Types**: Multi-leg strategies, spreads
3. **Risk Management**: Position sizing, stop losses
4. **Real-time Streaming**: WebSocket market data feeds
5. **GoTrade Integration**: API integration with trading platform

## Support

For issues related to:
- **IB API**: Check IB Client Portal Web API documentation
- **Application Bugs**: Review logs and error messages
- **Feature Requests**: Extend modular architecture

## License

This is a proof-of-concept implementation for educational and development purposes.
