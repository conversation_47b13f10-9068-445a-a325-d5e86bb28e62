# Interactive Brokers Options Trading - Python POC

A modular Python application for Interactive Brokers Options trading using the Client Portal Web API.

## Project Structure

```
├── main.py              # Main application with CLI menu
├── auth.py              # Authentication management
├── account.py           # Account balance management
├── positions.py         # Position tracking
├── options_search.py    # Option contract search and chains
├── market_data.py       # Market data and Greeks
├── orders.py            # Order placement and management
├── config.py            # Configuration settings
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## Features

### ✅ Implemented Functionality

1. **Authentication Management**
   - Check authentication status
   - Session management
   - Reauthentication support

2. **Account Management**
   - Fetch account balances relevant to options trading
   - Display formatted balance information
   - Account summary retrieval

3. **Position Management**
   - Fetch current open options positions
   - Display positions with P&L information
   - Filter options positions from all positions

4. **Options Search & Chains**
   - Search for specific option contracts
   - Fetch option chains for underlying symbols
   - Get available strikes and expirations

5. **Market Data**
   - Real-time option market data
   - Greeks (Delta, Gamma, Theta, Vega)
   - Bid/Ask spreads and volume
   - Underlying stock prices

6. **Order Management**
   - Place market orders for options
   - Place limit orders for options
   - Order validation before placement
   - Order status tracking
   - Order cancellation
   - Support for single-leg options trading

7. **Interactive CLI Menu**
   - User-friendly command-line interface
   - Menu-driven navigation
   - Error handling and validation

## Prerequisites

### 1. Interactive Brokers Setup

1. **Download and Install IB Gateway**
   - Download from: https://www.interactivebrokers.com/en/trading/tws.php
   - Install and configure for paper trading

2. **Configure Paper Trading Account**
   - Login to IB Gateway with paper trading credentials
   - Enable API access
   - Set port to 5000 (default)
   - Add 127.0.0.1 to trusted IPs

3. **Start Client Portal Gateway**
   ```bash
   ./clientportal.gw/bin/run.sh
   ```

### 2. Python Environment

1. **Install Python Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify IB Gateway is Running**
   - Navigate to https://localhost:5000 in your browser
   - Login with your IB credentials to establish session

## Usage

### Running the Application

```bash
python main.py
```

### Menu Options

```
===== Interactive Brokers Options Trading Menu =====
1. Fetch Account Balances
2. Fetch Open Option Positions
3. Fetch Option Chain for Symbol
4. Get Option Market Data
5. Place Option Order
6. Check Order Status
7. Cancel Order
8. Exit
=================================================
```

### Example Workflows

#### 1. Check Account Status
```
Choice: 1
```
Displays account balances including:
- Total Cash Value
- Available Funds
- Buying Power
- Option Market Value

#### 2. View Current Positions
```
Choice: 2
```
Shows all open options positions with:
- Symbol, expiry, strike, type
- Quantity and current price
- Unrealized P&L

#### 3. Research Options
```
Choice: 3
Enter underlying symbol: SPY
```
Displays available expirations and strikes for SPY options

#### 4. Get Market Data
```
Choice: 4
Enter underlying symbol: SPY
Enter expiry date: ********
Enter strike price: 580
Enter option type: C
```
Shows real-time market data and Greeks

#### 5. Place an Order
```
Choice: 5
Enter underlying symbol: SPY
Enter expiry date: ********
Enter strike price: 580
Enter option type: C
Enter side: BUY
Enter quantity: 1
Enter order type: LIMIT
Enter limit price: 5.50
```

## Configuration

Edit `config.py` to customize:

- **API Endpoints**: BASE_URL, ports
- **SSL Settings**: Certificate verification
- **Trading Parameters**: Time in force, order types
- **Market Data Fields**: Greeks and price fields
- **Logging**: Log levels and formats

## API Endpoints Used

| Endpoint | Purpose |
|----------|---------|
| `/iserver/auth/status` | Authentication status |
| `/iserver/account` | Account information |
| `/iserver/account/{id}/summary` | Account balances |
| `/iserver/account/positions/0` | Current positions |
| `/trsrv/secdef/search` | Option contract search |
| `/iserver/secdef/info` | Option chains |
| `/iserver/marketdata/snapshot` | Market data & Greeks |
| `/iserver/account/orders/whatif` | Order validation |
| `/iserver/account/orders` | Place/get orders |

## Error Handling

The application includes comprehensive error handling for:

- **Authentication Issues**: Automatic detection and guidance
- **Network Errors**: Connection timeouts and retries
- **Invalid Input**: User input validation
- **API Errors**: Detailed error messages from IB API
- **Order Rejections**: Clear feedback on order issues

## Logging

Logs are configured in `config.py`:
- **Level**: INFO (configurable)
- **Format**: Timestamp, module, level, message
- **Output**: Console (can be extended to files)

## Important Notes

1. **SSL Certificates**: IB uses self-signed certificates, so SSL verification is disabled
2. **Browser Authentication**: Initial login must be done via browser at https://localhost:5000
3. **Contract IDs**: All option orders require ConID for specific contracts
4. **Paper Trading**: This POC is designed for paper trading accounts
5. **Market Data**: Some Greeks may require market data subscriptions

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure IB Gateway is running
   - Check that Client Portal Gateway is started
   - Verify port 5000 is not blocked

2. **Authentication Failed**
   - Login via browser first at https://localhost:5000
   - Check paper trading account credentials
   - Verify API permissions are enabled

3. **Option Contract Not Found**
   - Verify expiry format (YYYYMMDD)
   - Check strike price exists
   - Ensure option type is 'C' or 'P'

4. **Order Rejected**
   - Check account has sufficient buying power
   - Verify option contract is actively traded
   - Ensure market is open for the security

## Next Steps

This Python POC provides the foundation for:

1. **C++ Implementation**: Port core functionality to C++ for performance
2. **Advanced Order Types**: Multi-leg strategies, spreads
3. **Risk Management**: Position sizing, stop losses
4. **Real-time Streaming**: WebSocket market data feeds
5. **GoTrade Integration**: API integration with trading platform

## Support

For issues related to:
- **IB API**: Check IB Client Portal Web API documentation
- **Application Bugs**: Review logs and error messages
- **Feature Requests**: Extend modular architecture

## License

This is a proof-of-concept implementation for educational and development purposes.
