"""
Test the fixed Interactive Brokers Options Trading Application
"""

import urllib3
from auth import IBAuth
from account import Account<PERSON>anager
from positions import PositionManager

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

def test_application():
    """Test all the main functionality"""
    print("=" * 60)
    print(" Testing Interactive Brokers Options Trading Application")
    print("=" * 60)
    
    # Test authentication
    print("\n[1/3] Testing Authentication...")
    auth = IBAuth()
    if auth.is_authenticated():
        print("✅ Authentication: SUCCESS")
        session = auth.get_session()
    else:
        print("❌ Authentication: FAILED")
        return False
    
    # Test account balances
    print("\n[2/3] Testing Account Balances...")
    try:
        account_manager = AccountManager(session)
        balances = account_manager.get_account_balances()
        
        if balances:
            print("✅ Account Balances: SUCCESS")
            print("\n===== Account Balances =====")
            for key, data in balances.items():
                value = data['value']
                currency = data['currency']
                print(f"{key:20}: {value:>15,.2f} {currency}")
            print("=" * 30)
        else:
            print("❌ Account Balances: FAILED")
            return False
    except Exception as e:
        print(f"❌ Account Balances: ERROR - {e}")
        return False
    
    # Test positions
    print("\n[3/3] Testing Positions...")
    try:
        position_manager = PositionManager(session)
        positions = position_manager.get_all_positions()
        
        if positions is not None:
            print(f"✅ Positions: SUCCESS ({len(positions)} positions)")
            if len(positions) == 0:
                print("   No positions found (normal for new paper trading account)")
        else:
            print("❌ Positions: FAILED")
            return False
    except Exception as e:
        print(f"❌ Positions: ERROR - {e}")
        return False
    
    print("\n" + "=" * 60)
    print(" 🎉 ALL TESTS PASSED! Your application is working correctly!")
    print("=" * 60)
    print("\nYou can now run: python main.py")
    print("And select option 1 to see your account balances.")
    
    return True

if __name__ == "__main__":
    test_application()
