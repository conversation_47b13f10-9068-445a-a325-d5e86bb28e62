"""
Diagnostic script to check IB Gateway setup and endpoint availability
"""

import requests
import urllib3
import subprocess
import sys

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

def check_gateway_processes():
    """Check if IB Gateway processes are running"""
    print("🔍 Checking for IB Gateway processes...")
    
    try:
        # Check for Java processes (IB Gateway runs on Java)
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq java.exe'], 
                              capture_output=True, text=True)
        
        if 'java.exe' in result.stdout:
            print("✅ Java processes found (IB Gateway likely running)")
            
            # Count Java processes
            java_count = result.stdout.count('java.exe')
            print(f"   Found {java_count} Java processes")
        else:
            print("❌ No Java processes found")
            print("   IB Gateway may not be running")
            
    except Exception as e:
        print(f"❌ Error checking processes: {e}")

def check_ports():
    """Check if required ports are open"""
    print("\n🔍 Checking port availability...")
    
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        
        # Check for port 5000 (Client Portal Gateway)
        if ':5000' in result.stdout:
            print("✅ Port 5000 (Client Portal Gateway) is active")
        else:
            print("❌ Port 5000 (Client Portal Gateway) not found")
        
        # Check for port 7497 (IB Gateway paper trading)
        if ':7497' in result.stdout:
            print("✅ Port 7497 (IB Gateway paper trading) is active")
        else:
            print("❌ Port 7497 (IB Gateway paper trading) not found")
            print("   This may be why option endpoints are failing")
        
        # Check for port 7496 (IB Gateway live trading)
        if ':7496' in result.stdout:
            print("✅ Port 7496 (IB Gateway live trading) is active")
        else:
            print("❌ Port 7496 (IB Gateway live trading) not found")
            
    except Exception as e:
        print(f"❌ Error checking ports: {e}")

def test_basic_endpoints():
    """Test basic IB API endpoints"""
    print("\n🔍 Testing basic API endpoints...")
    
    session = requests.Session()
    session.verify = False
    base_url = "https://localhost:5000/v1/api"
    
    endpoints = [
        ("/iserver/auth/status", "Authentication"),
        ("/iserver/account", "Account List"),
        ("/trsrv/secdef/search?symbol=SPY&secType=STK", "Stock Search"),
        ("/trsrv/secdef/search?symbol=SPY&secType=OPT", "Option Search"),
    ]
    
    for endpoint, name in endpoints:
        try:
            resp = session.get(f"{base_url}{endpoint}", timeout=10)
            if resp.ok:
                print(f"✅ {name}: Working ({resp.status_code})")
                
                # Show sample response
                try:
                    data = resp.json()
                    if isinstance(data, list) and len(data) > 0:
                        print(f"   Sample: {str(data[0])[:100]}...")
                    elif isinstance(data, dict):
                        print(f"   Keys: {list(data.keys())[:5]}")
                except:
                    print(f"   Response: {resp.text[:100]}...")
            else:
                print(f"❌ {name}: Failed ({resp.status_code})")
                if resp.status_code == 404:
                    print("   404 = Endpoint not found (may need IB Gateway)")
                elif resp.status_code == 401:
                    print("   401 = Not authenticated")
                    
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: Connection refused")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

def check_ib_gateway_requirements():
    """Check if IB Gateway is properly set up"""
    print("\n🔍 Checking IB Gateway requirements...")
    
    # Check if IB Gateway is installed
    import os
    
    # Common IB Gateway installation paths
    ib_paths = [
        r"C:\Jts",
        r"C:\Program Files\Interactive Brokers",
        r"C:\Users\<USER>\Jts".format(os.getenv('USERNAME')),
        r"C:\IBGateway"
    ]
    
    ib_found = False
    for path in ib_paths:
        if os.path.exists(path):
            print(f"✅ IB Gateway installation found: {path}")
            ib_found = True
            break
    
    if not ib_found:
        print("❌ IB Gateway installation not found")
        print("   Download from: https://www.interactivebrokers.com/en/trading/tws.php")

def provide_setup_recommendations():
    """Provide setup recommendations based on findings"""
    print("\n" + "="*60)
    print("💡 SETUP RECOMMENDATIONS")
    print("="*60)
    
    print("\n🎯 FOR FULL OPTIONS TRADING FUNCTIONALITY:")
    print("\n1. INSTALL IB GATEWAY:")
    print("   → Download: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php")
    print("   → Install IB Gateway (not just Client Portal Gateway)")
    
    print("\n2. CONFIGURE IB GATEWAY:")
    print("   → Start IB Gateway application")
    print("   → Login with your LIVE account credentials")
    print("   → Enable API in Configure → API → Settings")
    print("   → Set Socket Port to 7497 (paper trading)")
    print("   → Add 127.0.0.1 to Trusted IPs")
    
    print("\n3. RUN BOTH GATEWAYS:")
    print("   → Keep Client Portal Gateway running (port 5000)")
    print("   → Start IB Gateway (port 7497)")
    print("   → Both should run simultaneously")
    
    print("\n4. VERIFY SETUP:")
    print("   → Run this diagnostic script again")
    print("   → Check that both ports 5000 and 7497 are active")
    print("   → Test option endpoints")
    
    print("\n⚠️  IMPORTANT NOTES:")
    print("• Client Portal Gateway: Provides web API and authentication")
    print("• IB Gateway: Provides full trading API including options")
    print("• Both are needed for complete functionality")
    print("• The reference guide assumes both are running")

def main():
    """Run complete diagnostic"""
    print("🚀 Interactive Brokers Gateway Diagnostic")
    print("="*60)
    
    check_gateway_processes()
    check_ports()
    test_basic_endpoints()
    check_ib_gateway_requirements()
    provide_setup_recommendations()
    
    print("\n" + "="*60)
    print("🏁 DIAGNOSTIC COMPLETE")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nDiagnostic interrupted by user")
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {e}")
        print("Please check your setup and try again")
