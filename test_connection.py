"""
Test script to verify IB Gateway connection and authentication
"""

import requests
import urllib3
from config import BASE_URL, VERIFY_SSL

# Disable SSL warnings for self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning())

def test_connection():
    """Test basic connection to IB Gateway"""
    print("Testing connection to Interactive Brokers Gateway...")
    
    session = requests.Session()
    session.verify = VERIFY_SSL
    
    try:
        # Test basic connectivity
        print(f"Connecting to: {BASE_URL}")
        resp = session.get(f"{BASE_URL}/iserver/auth/status", timeout=10)
        
        if resp.ok:
            auth_data = resp.json()
            print("✅ Connection successful!")
            print(f"Authentication status: {auth_data}")
            
            if auth_data.get('authenticated', False):
                print("✅ Already authenticated")
                
                # Test account access
                accounts_resp = session.get(f"{BASE_URL}/iserver/account")
                if accounts_resp.ok:
                    accounts = accounts_resp.json()
                    print(f"✅ Found {len(accounts)} accounts")
                    for acc in accounts:
                        print(f"   Account ID: {acc.get('id')} - {acc.get('accountTitle', 'N/A')}")
                else:
                    print("❌ Failed to get account information")
                    
            else:
                print("❌ Not authenticated")
                print("Please login via browser at: https://localhost:5000")
                
        else:
            print(f"❌ Connection failed: {resp.status_code}")
            print(f"Response: {resp.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused")
        print("Make sure IB Gateway and Client Portal Gateway are running:")
        print("1. Start IB Gateway")
        print("2. Run: ./clientportal.gw/bin/run.sh")
        print("3. Login via browser at: https://localhost:5000")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_paper_trading_setup():
    """Test if paper trading is properly configured"""
    print("\nTesting paper trading setup...")
    
    session = requests.Session()
    session.verify = VERIFY_SSL
    
    try:
        resp = session.get(f"{BASE_URL}/iserver/account")
        if resp.ok:
            accounts = resp.json()
            for account in accounts:
                account_id = account.get('id', '')
                if 'DU' in account_id:  # Paper trading accounts typically start with DU
                    print(f"✅ Paper trading account detected: {account_id}")
                    return True
            
            print("⚠️  No paper trading account detected")
            print("Make sure you're logged into a paper trading account")
            return False
        else:
            print("❌ Could not check account type")
            return False
            
    except Exception as e:
        print(f"❌ Error checking paper trading setup: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Interactive Brokers Gateway Connection Test")
    print("=" * 60)
    
    test_connection()
    test_paper_trading_setup()
    
    print("\n" + "=" * 60)
    print("Test completed. If all tests pass, you can run: python main.py")
    print("=" * 60)
