"""
Windows Setup Script for Interactive Brokers Options Trading
This script helps download and configure the necessary components
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import json
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n[Step {step_num}] {description}")
    print("-" * 40)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("X Python 3.7 or higher is required")
        return False
    print(f"✓ Python {sys.version.split()[0]} detected")
    return True

def install_dependencies():
    """Install Python dependencies"""
    try:
        print("Installing Python dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("X Failed to install dependencies")
        return False

def download_file(url, filename):
    """Download a file from URL"""
    try:
        print(f"Downloading {filename}...")
        urllib.request.urlretrieve(url, filename)
        print(f"✓ Downloaded {filename}")
        return True
    except Exception as e:
        print(f"X Failed to download {filename}: {e}")
        return False

def download_client_portal_gateway():
    """Download Client Portal Gateway"""
    gateway_url = "https://download2.interactivebrokers.com/portal/clientportal.gw.zip"
    gateway_file = "clientportal.gw.zip"
    
    if os.path.exists("clientportal.gw"):
        print("✅ Client Portal Gateway already exists")
        return True
    
    if download_file(gateway_url, gateway_file):
        try:
            print("Extracting Client Portal Gateway...")
            with zipfile.ZipFile(gateway_file, 'r') as zip_ref:
                zip_ref.extractall(".")
            os.remove(gateway_file)
            print("✅ Client Portal Gateway extracted")
            return True
        except Exception as e:
            print(f"❌ Failed to extract gateway: {e}")
            return False
    return False

def create_gateway_scripts():
    """Create convenient scripts to start the gateway"""
    
    # Windows batch script
    batch_content = """@echo off
echo Starting Interactive Brokers Client Portal Gateway...
echo.
echo IMPORTANT: After starting, go to https://localhost:5000 in your browser
echo and login with your PAPER TRADING credentials
echo.
cd /d "%~dp0clientportal.gw"
java -cp "dist/*" ibgateway.GWClient
pause
"""
    
    with open("start_gateway.bat", "w") as f:
        f.write(batch_content)
    
    # Python script for cross-platform
    python_content = '''"""
Start Client Portal Gateway
"""
import subprocess
import sys
import os
from pathlib import Path

def start_gateway():
    gateway_path = Path("clientportal.gw")
    if not gateway_path.exists():
        print("❌ Client Portal Gateway not found. Run setup_windows.py first.")
        return False
    
    print("Starting Interactive Brokers Client Portal Gateway...")
    print()
    print("IMPORTANT: After starting, go to https://localhost:5000 in your browser")
    print("and login with your PAPER TRADING credentials")
    print()
    
    try:
        os.chdir(gateway_path)
        subprocess.run(["java", "-cp", "dist/*", "ibgateway.GWClient"])
    except KeyboardInterrupt:
        print("\\nGateway stopped by user")
    except Exception as e:
        print(f"❌ Error starting gateway: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_gateway()
'''
    
    with open("start_gateway.py", "w", encoding='utf-8') as f:
        f.write(python_content)
    
    print("✅ Gateway startup scripts created")

def check_java():
    """Check if Java is installed"""
    try:
        result = subprocess.run(["java", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Java is installed")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Java not found. Please install Java 8 or higher")
    print("   Download from: https://www.oracle.com/java/technologies/downloads/")
    return False

def create_config_file():
    """Create a configuration file with setup status"""
    config = {
        "setup_completed": True,
        "gateway_downloaded": os.path.exists("clientportal.gw"),
        "dependencies_installed": True,
        "java_available": check_java()
    }
    
    with open("setup_status.json", "w") as f:
        json.dump(config, f, indent=2)

def print_next_steps():
    """Print instructions for next steps"""
    print_header("SETUP COMPLETE - NEXT STEPS")
    
    print("1. Download and Install IB Gateway:")
    print("   → Go to: https://www.interactivebrokers.com/en/trading/ibgateway-stable.php")
    print("   → Download Windows version and install")
    print()
    
    print("2. Setup Paper Trading Account:")
    print("   → Login to Client Portal: https://portal.interactivebrokers.com")
    print("   → Go to Settings → Account Configuration → Paper Trading Account")
    print("   → Note your Paper Trading Username and reset password if needed")
    print()
    
    print("3. Start IB Gateway:")
    print("   → Double-click IB Gateway icon")
    print("   → Login with your LIVE account credentials")
    print("   → Configure API: Enable API, Port 7497, Trusted IP 127.0.0.1")
    print()
    
    print("4. Start Client Portal Gateway:")
    print("   → Run: start_gateway.bat (or python start_gateway.py)")
    print("   → Go to: https://localhost:5000")
    print("   → Login with PAPER TRADING credentials")
    print()
    
    print("5. Test Connection:")
    print("   → Run: python test_connection.py")
    print()
    
    print("6. Start Trading Application:")
    print("   → Run: python main.py")

def main():
    """Main setup function"""
    print_header("Interactive Brokers Options Trading Setup")
    print("This script will help you set up the required components")
    
    # Check Python version
    print_step(1, "Checking Python Version")
    if not check_python_version():
        return False
    
    # Check Java
    print_step(2, "Checking Java Installation")
    java_ok = check_java()
    
    # Install dependencies
    print_step(3, "Installing Python Dependencies")
    if not install_dependencies():
        return False
    
    # Download Client Portal Gateway
    print_step(4, "Downloading Client Portal Gateway")
    if not download_client_portal_gateway():
        return False
    
    # Create startup scripts
    print_step(5, "Creating Gateway Startup Scripts")
    create_gateway_scripts()
    
    # Create config file
    create_config_file()
    
    # Print next steps
    print_next_steps()
    
    if not java_ok:
        print("\n⚠️  WARNING: Java not detected. Please install Java before proceeding.")
    
    print("\n✅ Setup completed successfully!")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nSetup interrupted by user")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
