"""
Configuration settings for Interactive Brokers Options Trading Application
"""

# IB Gateway Configuration
BASE_URL = "https://localhost:5000/v1/api"
IB_GATEWAY_PORT = 5000
IB_GATEWAY_HOST = "localhost"

# SSL Configuration (IB uses self-signed certificates)
VERIFY_SSL = False

# Trading Configuration
DEFAULT_TIF = "DAY"  # Time in Force
DEFAULT_OUTSIDE_RTH = False  # Outside Regular Trading Hours
USE_ADAPTIVE = False

# Market Data Fields for Options
OPTION_MARKET_DATA_FIELDS = "31,84,86,87,7295,7296,7308,7309"  # Price, volume, and Greeks

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Order Types
ORDER_TYPES = {
    "MARKET": "MKT",
    "LIMIT": "LMT",
    "STOP": "STP",
    "STOP_LIMIT": "STP LMT"
}

# Option Rights
OPTION_RIGHTS = {
    "CALL": "C",
    "PUT": "P"
}

# Order Sides
ORDER_SIDES = {
    "BUY": "BUY",
    "SELL": "SELL"
}
